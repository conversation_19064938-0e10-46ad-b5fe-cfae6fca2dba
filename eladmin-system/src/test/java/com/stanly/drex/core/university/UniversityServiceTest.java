package com.stanly.drex.core.university;

import com.stanly.drex.core.university.domain.University;
import com.stanly.drex.core.university.service.UniversityService;
import com.stanly.drex.core.university.service.dto.UniversityDto;
import com.stanly.drex.core.university.service.dto.UniversityQueryCriteria;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 高校域名白名单服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class UniversityServiceTest {

    @Resource
    private UniversityService universityService;

    @Test
    public void testCreateUniversity() {
        // 创建测试数据
        University university = new University();
        university.setUniversity("北京大学");
        university.setDomain("pku.edu.cn");

        // 执行创建
        UniversityDto result = universityService.create(university);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("北京大学", result.getUniversity());
        assertEquals("pku.edu.cn", result.getDomain());
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());
    }

    @Test
    public void testCreateDuplicateUniversity() {
        // 创建第一个记录
        University university1 = new University();
        university1.setUniversity("清华大学");
        university1.setDomain("tsinghua.edu.cn");
        universityService.create(university1);

        // 尝试创建重复的学校名称
        University university2 = new University();
        university2.setUniversity("清华大学");
        university2.setDomain("thu.edu.cn");

        // 应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            universityService.create(university2);
        });
    }

    @Test
    public void testCreateDuplicateDomain() {
        // 创建第一个记录
        University university1 = new University();
        university1.setUniversity("复旦大学");
        university1.setDomain("fudan.edu.cn");
        universityService.create(university1);

        // 尝试创建重复的域名
        University university2 = new University();
        university2.setUniversity("复旦");
        university2.setDomain("fudan.edu.cn");

        // 应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            universityService.create(university2);
        });
    }

    @Test
    public void testQueryUniversity() {
        // 创建测试数据
        University university = new University();
        university.setUniversity("上海交通大学");
        university.setDomain("sjtu.edu.cn");
        UniversityDto created = universityService.create(university);

        // 查询数据
        UniversityDto found = universityService.findById(created.getId());

        // 验证结果
        assertNotNull(found);
        assertEquals(created.getId(), found.getId());
        assertEquals("上海交通大学", found.getUniversity());
        assertEquals("sjtu.edu.cn", found.getDomain());
    }

    @Test
    public void testUpdateUniversity() {
        // 创建测试数据
        University university = new University();
        university.setUniversity("中山大学");
        university.setDomain("sysu.edu.cn");
        UniversityDto created = universityService.create(university);

        // 更新数据
        University updateData = new University();
        updateData.setId(created.getId());
        updateData.setUniversity("中山大学");
        updateData.setDomain("mail.sysu.edu.cn");

        // 执行更新
        universityService.update(updateData);

        // 验证更新结果
        UniversityDto updated = universityService.findById(created.getId());
        assertEquals("mail.sysu.edu.cn", updated.getDomain());
    }

    @Test
    public void testDeleteUniversity() {
        // 创建测试数据
        University university = new University();
        university.setUniversity("华中科技大学");
        university.setDomain("hust.edu.cn");
        UniversityDto created = universityService.create(university);

        // 删除数据
        universityService.deleteAll(new Long[]{created.getId()});

        // 验证删除结果
        assertThrows(RuntimeException.class, () -> {
            universityService.findById(created.getId());
        });
    }

    @Test
    public void testQueryWithCriteria() {
        // 创建测试数据
        University university1 = new University();
        university1.setUniversity("西安交通大学");
        university1.setDomain("xjtu.edu.cn");
        universityService.create(university1);

        University university2 = new University();
        university2.setUniversity("西北工业大学");
        university2.setDomain("nwpu.edu.cn");
        universityService.create(university2);

        // 按学校名称查询
        UniversityQueryCriteria criteria = new UniversityQueryCriteria();
        criteria.setUniversity("西安");
        var result = universityService.queryAll(criteria);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("西安交通大学", result.get(0).getUniversity());

        // 按域名查询
        criteria = new UniversityQueryCriteria();
        criteria.setDomain("nwpu");
        result = universityService.queryAll(criteria);

        // 验证结果
        assertEquals(1, result.size());
        assertEquals("西北工业大学", result.get(0).getUniversity());
    }
}
