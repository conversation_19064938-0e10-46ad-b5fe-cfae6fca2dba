///*
//*  Copyright 2019-2020 <PERSON>
//*
//*  Licensed under the Apache License, Version 2.0 (the "License");
//*  you may not use this file except in compliance with the License.
//*  You may obtain a copy of the License at
//*
//*  http://www.apache.org/licenses/LICENSE-2.0
//*
//*  Unless required by applicable law or agreed to in writing, software
//*  distributed under the License is distributed on an "AS IS" BASIS,
//*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//*  See the License for the specific language governing permissions and
//*  limitations under the License.
//*/
//package com.stanly.quests.task.user.rest;
//
//import com.stanly.admin.annotation.Log;
//import com.stanly.admin.utils.PageResult;
//import com.stanly.quests.task.icon.service.TaskIconService;
//import com.stanly.quests.task.icon.service.dto.TaskIconDto;
//import com.stanly.quests.task.icon.service.dto.TaskIconQueryCriteria;
//import com.stanly.quests.task.user.service.TaskUserService;
//import com.stanly.quests.task.user.service.dto.TaskUserDto;
//import com.stanly.quests.task.user.service.dto.TaskUserQueryCriteria;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.RequiredArgsConstructor;
//import org.springframework.data.domain.Pageable;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
//* @website https://eladmin.vip
//* <AUTHOR>
//* @date 2024-09-25
//**/
//@RestController
//@RequiredArgsConstructor
//@Tag(name = "TaskUserController管理")
//@RequestMapping("/api/taskUser")
//public class TaskUserController {
//
//    private final TaskUserService taskUserService;
//
//    @GetMapping
//    @Log("查询TaskUserController")
//    @Operation(summary = "查询TaskUserController")
//    @PreAuthorize("@el.check('taskUser:list')")
//    public ResponseEntity<PageResult<TaskUserDto>> queryTaskUser(TaskUserQueryCriteria criteria, Pageable pageable){
//        return new ResponseEntity<>(taskUserService.queryTaskUser(criteria,pageable),HttpStatus.OK);
//    }
//
//}