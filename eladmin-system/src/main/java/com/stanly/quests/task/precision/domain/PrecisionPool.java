/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.precision.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;
import java.util.List;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-03-20
**/
@Entity
@Data
@Table(name="precision_pool")
public class PrecisionPool implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "投放id,目前写死1")
    private String id;

    @Column(name = "`channel`",nullable = false)
    @NotBlank
    @Schema(name = "投放渠道,twitter")
    private String channel;

    @Column(name = "`task`")
    @Schema(name = "关联任务/活动,保留字段")
    private String task;

    @Column(name = "`owner`",nullable = false)
    @NotBlank
    @Schema(name = "所属者,edgen目前写死")
    private String owner;

    @Column(name = "`distribution_amount`",nullable = false)
    @NotNull
    @Schema(name = "distribution投放金额,3000")
    private Double distributionAmount;

    @Column(name = "`contribution_ai_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_ai投放金额,2100")
    private Double contributionAiAmount;

    @Column(name = "`contribution_human_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_human投放金额,4200")
    private Double contributionHumanAmount;

    @Column(name = "`contribution_deep_human_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_deep_human投放金额,700 * 6")
    private Double contributionDeepHumanAmount;

    @Column(name = "`distribution_max_amount`",nullable = false)
    @NotNull
    @Schema(name = "distribution用户获得的最大金额,30")
    private Double distributionMaxAmount;

    @Column(name = "`contribution_ai_max_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_ai单个用户最大金额,21")
    private Double contributionAiMaxAmount;

    @Column(name = "`contribution_human_max_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_human单个用户最大金额,42")
    private Double contributionHumanMaxAmount;

    @Column(name = "`contribution_deep_human_max_amount`",nullable = false)
    @NotNull
    @Schema(name = "contribution_deep_human单个用户最大值,7 * 6")
    private Double contributionDeepHumanMaxAmount;

    @Column(name = "`precision_track_check_follows_count`",nullable = false)
    @NotNull
    @Schema(name = "资深用户粉丝数")
    private Double precisionTrackCheckFollowsCount;

    @Column(name = "`precision_track_check_ai_point`",nullable = false)
    @NotNull
    @Schema(name = "ai最少打分数")
    private Double precisionTrackCheckAiPoint;

    @Column(name = "`precision_track_check_asset`",nullable = false)
    @NotNull
    @Schema(name = "资深用户aura值")
    private Double precisionTrackCheckAsset;

    public void copy(PrecisionPool source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
