package com.stanly.quests.task.config.service.impl;

import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.stanly.admin.utils.RedisUtils;
import com.stanly.quests.task.config.domain.TaskConfig;
import com.stanly.quests.task.config.repository.TaskConfigRepository;
import com.stanly.quests.task.config.service.TaskConfigService;
import com.stanly.quests.task.config.service.proxy.TaskRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskConfigPublish {

    private final RedisUtils redisUtils;
    private final TaskConfigService taskConfigService;
    private final TaskRemoteService taskRemoteService;
    private final TaskConfigRepository taskConfigRepository;

    public void publish() {
        Map<Object, Object> timingPublish = redisUtils.hmget("timing_publish");
        timingPublish.forEach((k, v) -> {
            if(Long.parseLong(String.valueOf(v)) <= System.currentTimeMillis()) {
                log.info("timing publish task config : {}", k);
                publish(String.valueOf(k));
            }
        });
    }

    private void publish(String taskId) {
        TaskConfigDTO configDTO = taskConfigService.findByTaskId(taskId);
        taskRemoteService.syncTaskConfig(configDTO, false);

        TaskConfig taskConfig = taskConfigRepository.findByTaskId(configDTO.getTaskId());
        taskConfig.setStatus("ACTIVE");
        taskConfigRepository.save(taskConfig);

        redisUtils.hdel("timing_publish", taskId);
    }
}
