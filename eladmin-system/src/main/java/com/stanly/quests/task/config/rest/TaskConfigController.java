/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.rest;

import com.alibaba.fastjson.JSON;
import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.stanly.admin.annotation.Log;
import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.config.domain.TaskConfigManagerDTO;
import com.stanly.quests.task.config.domain.TimingPublishDTO;
import com.stanly.quests.task.config.service.TaskConfigService;
import com.stanly.quests.task.config.service.dto.TaskConfigDto;
import com.stanly.quests.task.config.service.dto.TaskConfigQueryCriteria;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2024-08-02
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "任务配置接口管理")
@RequestMapping("/api/taskConfig")
public class TaskConfigController {

    private final TaskConfigService taskConfigService;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskConfig:list')")
    public void exportTaskConfig(HttpServletResponse response, TaskConfigQueryCriteria criteria) throws IOException {
        taskConfigService.download(taskConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询任务配置接口")
    @Operation(summary = "查询任务配置接口")
    @PreAuthorize("@el.check('taskConfig:list')")
    public ResponseEntity<PageResult<TaskConfigDto>> queryTaskConfig(TaskConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskConfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增任务配置接口")
    @Operation(summary = "新增任务配置接口")
    @PreAuthorize("@el.check('taskConfig:add')")
    public ResponseEntity<Object> createTaskConfig(@Validated @RequestBody TaskConfigManagerDTO resources){
        taskConfigService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改任务配置接口")
    @Operation(summary = "修改任务配置接口")
    @PreAuthorize("@el.check('taskConfig:edit')")
    public ResponseEntity<Object> updateTaskConfig(@Validated @RequestBody TaskConfigManagerDTO resources){
        taskConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除任务配置接口")
    @Operation(summary = "删除任务配置接口")
    @PreAuthorize("@el.check('taskConfig:del')")
    public ResponseEntity<Object> deleteTaskConfig(@RequestBody String[] ids) {
        taskConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/releasePublish")
    @Log("任务正式发布")
    @Operation(summary = "任务正式发布")
    @PreAuthorize("@el.check('taskConfig:edit')")
    public ResponseEntity<Object> releasePublish(@Validated @RequestBody TaskConfigManagerDTO resources){
        resources.setStatus("ACTIVE");
        taskConfigService.update(resources);
        boolean res = taskConfigService.publish(resources, false);
        if(res){
            return new ResponseEntity<>("发布成功", HttpStatus.OK);
        }else{
            return new ResponseEntity<>("发布失败", HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/greyPublish")
    @Log("任务灰度发布")
    @Operation(summary = "任务灰度发布")
    @PreAuthorize("@el.check('taskConfig:edit')")
    public ResponseEntity<Object> greyPublish(@Validated @RequestBody TaskConfigManagerDTO resources) {
        resources.setStatus("GRAY");
        taskConfigService.update(resources);
        boolean res = taskConfigService.publish(resources, true);
        if(res){
            return new ResponseEntity<>("灰度成功", HttpStatus.OK);
        }else{
            return new ResponseEntity<>("灰度失败", HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/timingPublish")
    @Log("任务定时发布")
    @Operation(summary = "任务定时发布")
    @PreAuthorize("@el.check('taskConfig:edit')")
    public ResponseEntity<Object> timingPublish(@Validated @RequestBody TimingPublishDTO resources) {
        taskConfigService.timingPublish(resources);
        return new ResponseEntity<>("定时发布成功", HttpStatus.OK);
    }

    @GetMapping("/exportJson/{id}")
    @Log("导出json")
    @Operation(summary = "导出json")
    public ResponseEntity<Object> exportJson(@PathVariable("id") String id) {
        TaskConfigDTO configDto = taskConfigService.findByTaskId(id);
        return ResponseEntity.ok(List.of(configDto));
    }

    @GetMapping("/exportAllJson/{ids}")
    @Log("批量导出json")
    @Operation(summary = "批量导出json")
    public ResponseEntity<Object> exportAllJson(@PathVariable String ids) {
        List<TaskConfigDTO> configDto = taskConfigService.findByTaskIds(Arrays.asList(ids.split(",")));
        return ResponseEntity.ok(configDto);
    }

    @PostMapping("/importJson")
    @Log("导入json")
    @Operation(summary = "导入json")
    public ResponseEntity<Object> importJson(@RequestBody String date){
        List<TaskConfigDTO> taskConfigDTOs = JSON.parseArray(date, TaskConfigDTO.class);
        for(TaskConfigDTO taskConfigDTO: taskConfigDTOs){
            taskConfigService.importJSON(taskConfigDTO);
        }
        return ResponseEntity.ok("导入成功");
    }
}