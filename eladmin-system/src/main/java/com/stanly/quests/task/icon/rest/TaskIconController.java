/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.icon.rest;

import com.aliyun.oss.model.PutObjectResult;
import com.stanly.admin.annotation.Log;
import com.stanly.admin.utils.FileUtil;
import com.stanly.admin.utils.UploadAliyunOssUtil;
import com.stanly.quests.task.icon.domain.TaskIcon;
import com.stanly.quests.task.icon.service.TaskIconService;
import com.stanly.quests.task.icon.service.dto.TaskIconQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.File;
import java.io.IOException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.icon.service.dto.TaskIconDto;
import org.springframework.web.multipart.MultipartFile;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2024-09-25
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "TaskIconController管理")
@RequestMapping("/api/taskIcon")
public class TaskIconController {

    private final TaskIconService taskIconService;
    private final UploadAliyunOssUtil uploadAliyunOssUtil;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskIcon:list')")
    public void exportTaskIcon(HttpServletResponse response, TaskIconQueryCriteria criteria) throws IOException {
        taskIconService.download(taskIconService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询TaskIconController")
    @Operation(summary = "查询TaskIconController")
    @PreAuthorize("@el.check('taskIcon:list')")
    public ResponseEntity<PageResult<TaskIconDto>> queryTaskIcon(TaskIconQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(taskIconService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增TaskIconController")
    @Operation(summary = "新增TaskIconController")
    @PreAuthorize("@el.check('taskIcon:add')")
    public ResponseEntity<Object> createTaskIcon(@Validated @RequestBody TaskIcon resources){
        taskIconService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改TaskIconController")
    @Operation(summary = "修改TaskIconController")
    @PreAuthorize("@el.check('taskIcon:edit')")
    public ResponseEntity<Object> updateTaskIcon(@Validated @RequestBody TaskIcon resources){
        taskIconService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除TaskIconController")
    @Operation(summary = "删除TaskIconController")
    @PreAuthorize("@el.check('taskIcon:del')")
    public ResponseEntity<Object> deleteTaskIcon(@RequestBody String[] ids) {
        taskIconService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "upload")
    @Log("上传task icon")
    @Operation(summary = "上传task icon")
    public ResponseEntity<Object> uploadIcon(MultipartFile file, HttpServletRequest request){
        String saasId = request.getParameter("saasId");
        String code = request.getParameter("code");
        File f = FileUtil.toFile(file);
        String fileName = code + f.getName().substring(f.getName().lastIndexOf("."));
        PutObjectResult result = uploadAliyunOssUtil.putObject("drex/" + saasId, fileName, f);
        return ResponseEntity.ok(uploadAliyunOssUtil.getLocation("drex/" + saasId, fileName));
    }
}