/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.stanly.quests.task.whitelist.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.stanly.admin.constants.RedisHashKeyConstants;
import com.stanly.admin.utils.*;
import com.stanly.quests.task.whitelist.domain.TaskWhitelist;
import com.stanly.quests.task.whitelist.repository.TaskWhitelistRepository;
import com.stanly.quests.task.whitelist.service.TaskWhitelistService;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistDto;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistQueryCriteria;
import com.stanly.quests.task.whitelist.service.mapstruct.TaskWhitelistMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @website https://eladmin.vip
 * @description 服务实现
 * @date 2024-09-25
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskWhitelistServiceImpl implements TaskWhitelistService {

    private final TaskWhitelistRepository taskWhitelistRepository;
    private final TaskWhitelistMapper taskWhitelistMapper;
    private final RedisUtils redisUtils;

    @Override
    public PageResult<TaskWhitelistDto> queryAll(TaskWhitelistQueryCriteria criteria, Pageable pageable) {
        Page<TaskWhitelist> page = taskWhitelistRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(taskWhitelistMapper::toDto));
    }

    @Override
    public List<TaskWhitelistDto> queryAll(TaskWhitelistQueryCriteria criteria) {
        return taskWhitelistMapper.toDto(taskWhitelistRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskWhitelistDto findById(String id) {
        TaskWhitelist taskWhitelist = taskWhitelistRepository.findById(id).orElseGet(TaskWhitelist::new);
        ValidationUtil.isNull(taskWhitelist.getId(), "TaskWhitelist", "id", id);
        return taskWhitelistMapper.toDto(taskWhitelist);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(TaskWhitelist resources) {
        resources.setId(IdUtil.simpleUUID());
        log.info("add task whitelist: {}", resources);
        taskWhitelistRepository.save(resources);
        redisUtils.hset(RedisHashKeyConstants.TASK_WHITELIST_HASH_KEY, JSON.toJSONString(resources.getFlag()), resources.getRemark());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskWhitelist resources) {
        TaskWhitelist taskWhitelist = taskWhitelistRepository.findById(resources.getId()).orElseGet(TaskWhitelist::new);
        log.info("update task whitelist: {}", taskWhitelist);
        ValidationUtil.isNull(taskWhitelist.getId(), "TaskWhitelist", "id", resources.getId());
        taskWhitelist.copy(resources);
        taskWhitelistRepository.save(taskWhitelist);
        redisUtils.hset(RedisHashKeyConstants.TASK_WHITELIST_HASH_KEY, JSON.toJSONString(taskWhitelist.getFlag()), resources.getRemark());
    }

    @Override
    public void deleteAll(String[] ids) {
        log.info("delete task whitelist: {}", JSON.toJSONString(ids));
        for (String id : ids) {
            Optional<TaskWhitelist> byId = taskWhitelistRepository.findById(id);
            if (byId.isPresent()) {
                TaskWhitelist taskWhitelist = byId.get();

                redisUtils.hdel(RedisHashKeyConstants.TASK_WHITELIST_HASH_KEY, JSON.toJSONString(taskWhitelist.getFlag()));
                taskWhitelistRepository.deleteById(id);
            }
        }
    }

    @Override
    public void download(List<TaskWhitelistDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskWhitelistDto taskWhitelist : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" id", taskWhitelist.getId());
            map.put(" saasId", taskWhitelist.getSaasId());
            map.put(" flag", taskWhitelist.getFlag());
            map.put(" remark", taskWhitelist.getRemark());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}