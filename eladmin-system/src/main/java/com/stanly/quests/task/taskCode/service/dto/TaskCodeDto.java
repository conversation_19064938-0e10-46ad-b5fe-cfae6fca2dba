/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.taskCode.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-11-07
**/
@Data
public class TaskCodeDto implements Serializable {

    /** 数据主键 */
    private String id;

    /** 任务事件 */
    private String code;

    /** 主任务事件 */
    private String mainCode;

    /** 增量值 */
    private Integer inc;

    /** 任务事件描述 */
    private String desc;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp modifiedTime;

    /** 拆分子事件 */
    private String splitCode;
}