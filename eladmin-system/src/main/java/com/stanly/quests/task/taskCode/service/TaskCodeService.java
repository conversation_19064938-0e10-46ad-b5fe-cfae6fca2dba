/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.taskCode.service;

import com.stanly.quests.task.taskCode.domain.TaskCode;
import com.stanly.quests.task.taskCode.service.dto.TaskCodeDto;
import com.stanly.quests.task.taskCode.service.dto.TaskCodeQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-11-07
**/
public interface TaskCodeService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<TaskCodeDto> queryAll(TaskCodeQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskCodeDto>
    */
    List<TaskCodeDto> queryAll(TaskCodeQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param code ID
     * @return TaskCodeDto
     */
    TaskCodeDto findById(String code);

    /**
    * 创建
    * @param resources /
    */
    void create(TaskCode resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskCode resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskCodeDto> all, HttpServletResponse response) throws IOException;
}