/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.icon.service;

import com.stanly.quests.task.icon.domain.TaskIcon;
import com.stanly.quests.task.icon.service.dto.TaskIconDto;
import com.stanly.quests.task.icon.service.dto.TaskIconQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-09-25
**/
public interface TaskIconService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<TaskIconDto> queryAll(TaskIconQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskIconDto>
    */
    List<TaskIconDto> queryAll(TaskIconQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return TaskIconDto
     */
    TaskIconDto findById(String id);

    /**
    * 创建
    * @param resources /
    */
    void create(TaskIcon resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskIcon resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskIconDto> all, HttpServletResponse response) throws IOException;
}