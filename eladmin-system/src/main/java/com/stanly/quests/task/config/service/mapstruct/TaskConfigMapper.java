/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service.mapstruct;

import com.alibaba.fastjson.JSON;
import com.stanly.admin.base.BaseMapper;
import com.stanly.admin.utils.StringUtils;
import com.stanly.quests.task.config.domain.Reward;
import com.stanly.quests.task.config.domain.TaskConfig;
import com.stanly.quests.task.config.service.dto.TaskConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.ArrayList;
import java.util.List;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2024-08-02
**/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskConfigMapper extends BaseMapper<TaskConfigDto, TaskConfig> {

    @Mapping(target = "rewards", expression = "java(MapStruct.rewardsToList(taskConfig.getRewards()))")
    TaskConfigDto toDto(TaskConfig taskConfig);

    /**
     * DTO转Entity
     *
     * @param dto /
     * @return /
     */
    @Mapping(target = "rewards", expression = "java(MapStruct.rewardsToStr(dto.getRewards()))")
    TaskConfig toEntity(TaskConfigDto dto);

    class MapStruct {
        public static List<Reward> rewardsToList(String rewards) {
            if(StringUtils.isBlank(rewards)){
                return new ArrayList<>();
            }
            return JSON.parseArray(rewards, Reward.class);
        }

        public static String rewardsToStr(List<Reward> rewards) {
            if(rewards != null){
                return JSON.toJSONString(rewards);
            }
            return null;
        }
    }
}