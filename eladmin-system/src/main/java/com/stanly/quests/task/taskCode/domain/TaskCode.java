/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.taskCode.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import org.hibernate.annotations.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-11-07
**/
@Entity
@Data
@Table(name="task_code")
public class TaskCode implements Serializable {

    @Column(name = "`id`")
    @Schema(name = "数据主键")
    private String id;

    @Id
    @Column(name = "`code`")
    @Schema(name = "任务事件")
    private String code;

    @Column(name = "`main_code`",nullable = false)
    @NotBlank
    @Schema(name = "主任务事件")
    private String mainCode;

    @Column(name = "`inc`",nullable = false)
    @NotNull
    @Schema(name = "增量值")
    private Integer inc;

    @Column(name = "`desc`",nullable = false)
    @NotBlank
    @Schema(name = "任务事件描述")
    private String desc;

    @Column(name = "`create_time`")
    @CreationTimestamp
    @Schema(name = "创建时间")
    private Timestamp createTime;

    @Column(name = "`modified_time`")
    @UpdateTimestamp
    @Schema(name = "修改时间")
    private Timestamp modifiedTime;

    @Column(name = "`split_code`")
    @Schema(name = "拆分子事件")
    private String splitCode;

    public void copy(TaskCode source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
