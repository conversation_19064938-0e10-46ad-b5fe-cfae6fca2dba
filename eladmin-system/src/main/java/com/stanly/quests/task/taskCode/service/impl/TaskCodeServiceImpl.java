/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.taskCode.service.impl;

import com.stanly.quests.task.taskCode.domain.TaskCode;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.admin.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.stanly.quests.task.taskCode.repository.TaskCodeRepository;
import com.stanly.quests.task.taskCode.service.TaskCodeService;
import com.stanly.quests.task.taskCode.service.dto.TaskCodeDto;
import com.stanly.quests.task.taskCode.service.dto.TaskCodeQueryCriteria;
import com.stanly.quests.task.taskCode.service.mapstruct.TaskCodeMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-11-07
**/
@Service
@RequiredArgsConstructor
public class TaskCodeServiceImpl implements TaskCodeService {

    private final TaskCodeRepository taskCodeRepository;
    private final TaskCodeMapper taskCodeMapper;

    @Override
    public PageResult<TaskCodeDto> queryAll(TaskCodeQueryCriteria criteria, Pageable pageable){
        Page<TaskCode> page = taskCodeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskCodeMapper::toDto));
    }

    @Override
    public List<TaskCodeDto> queryAll(TaskCodeQueryCriteria criteria){
        return taskCodeMapper.toDto(taskCodeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskCodeDto findById(String code) {
        TaskCode taskCode = taskCodeRepository.findById(code).orElseGet(TaskCode::new);
        ValidationUtil.isNull(taskCode.getCode(),"TaskCode","code",code);
        return taskCodeMapper.toDto(taskCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(TaskCode resources) {
        resources.setId(IdUtil.simpleUUID()); 
        taskCodeRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskCode resources) {
        TaskCode taskCode = taskCodeRepository.findById(resources.getCode()).orElseGet(TaskCode::new);
        ValidationUtil.isNull( taskCode.getCode(),"TaskCode","code",resources.getCode());
        taskCode.copy(resources);
        taskCodeRepository.save(taskCode);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String code : ids) {
            taskCodeRepository.deleteById(code);
        }
    }

    @Override
    public void download(List<TaskCodeDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskCodeDto taskCode : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("任务事件", taskCode.getCode());
            map.put("主任务事件", taskCode.getMainCode());
            map.put("增量值", taskCode.getInc());
            map.put("任务事件描述", taskCode.getDesc());
            map.put("创建时间", taskCode.getCreateTime());
            map.put("修改时间", taskCode.getModifiedTime());
            map.put("拆分子事件", taskCode.getSplitCode());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}