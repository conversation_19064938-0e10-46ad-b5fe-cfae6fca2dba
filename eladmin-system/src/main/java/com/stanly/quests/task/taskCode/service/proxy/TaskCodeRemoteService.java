//package com.stanly.quests.task.taskCode.service.proxy;
//
//import com.kikitrade.activity.api.RemoteManageActivityConfigService;
//import com.kikitrade.activity.api.model.TaskCodeConfigDTO;
//import com.stanly.admin.base.system.domain.Dict;
//import com.stanly.admin.base.system.domain.DictDetail;
//import com.stanly.admin.base.system.service.DictDetailService;
//import com.stanly.admin.base.system.service.DictService;
//import com.stanly.admin.base.system.service.dto.DictDto;
//import com.stanly.quests.task.taskCode.domain.TaskCode;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Service;
//
///**
// * <AUTHOR>
// * @desc
// * @date 2024/9/30 15:42
// */
//@Service
//public class TaskCodeRemoteService {
//
//    @DubboReference
//    private RemoteManageActivityConfigService remoteManageActivityConfigService;
//
//    public Boolean SyncTaskCode(TaskCode resources){
//
//        TaskCodeConfigDTO codeConfigDTO = new TaskCodeConfigDTO();
//        codeConfigDTO.setCode(resources.getCode());
//        codeConfigDTO.setMainCode(resources.getMainCode());
//        codeConfigDTO.setInc(resources.getInc());
//        codeConfigDTO.setDesc(resources.getDesc());
//        codeConfigDTO.setSplitCode(resources.getSplitCode());
//        return remoteManageActivityConfigService.syncTaskCode(codeConfigDTO).success;
//    }
//}
