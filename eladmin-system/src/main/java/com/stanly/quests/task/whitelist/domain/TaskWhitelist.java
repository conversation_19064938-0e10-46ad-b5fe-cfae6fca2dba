/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.whitelist.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-12-02
**/
@Entity
@Data
@Table(name="task_whitelist")
public class TaskWhitelist implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "ID")
    private String id;

    @Column(name = "`saas_id`")
    @Schema(name = "SaaS ID")
    private String saasId;

    @Column(name = "`flag`")
    @Schema(name = "用户id")
    private String flag;

    @Column(name = "`remark`")
    @Schema(name = "备注")
    private String remark;

    public void copy(TaskWhitelist source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
