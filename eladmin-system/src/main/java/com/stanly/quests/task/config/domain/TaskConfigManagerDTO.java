package com.stanly.quests.task.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/26 16:45
 */
@Data
public class TaskConfigManagerDTO implements Serializable {

    @Schema(name = "数据主键")
    private String id;
    @NotBlank
    @Schema(name = "appId")
    private String appId;

    @Schema(name = "任务id")
    private String taskId;

    @Schema(name = "是否列表展示(0 否 1 是)")
    private String showList;

    @Schema(name = "积分流水描述")
    private String ledgerTitle;

    @Schema(name = "任务标题")
    private String title;

    @Schema(name = "任务描述")
    private String desc;


    @Schema(name = "任务状态(ACTIVE、DISABLE)")
    private String status;

    @Schema(name = "任务开始时间，utc毫秒时间戳")
    private Timestamp startTime;

    @Schema(name = "任务结束时间，utc毫秒时间戳")
    private Timestamp endTime;

    @Schema(name = "任务事件编号")
    private String code;


    @Schema(name = "发奖频率，每完成n次任务，发一次奖")
    private Integer rewardFrequency;

    @Schema(name = "任务刷新周期")
    private String cycle;

    @Schema(name = "任务排序")
    private Integer order;

    @Schema(name = "任务所属模块twitter、discord")
    private String domain;

    @Schema(name = "任务要求的vip等级，+0: 普通用户以上，1: 会员专享")
    private String vipLevel;

    @Schema(name = "前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮")
    private Integer btn;

    @Schema(name = "是否显示任务showProgress进度条")
    private String showProgress;

    @Schema(name = "任务的详情页")
    private String url;

    @Schema(name = "奖励类型")
    private String rewardType;

    @Schema(name = "奖励金额")
    private String rewardAmount;

    @Schema(name = "展示奖励数量")
    private String showRewardAmount;

    @Schema(name = "奖励币种")
    private String rewardCurrency;

    @Schema(name = "奖励")
    private List<Reward> rewards;

    @Schema(name = "计划发布时间")
    private Timestamp publishTime;

    @Schema(name = "任务授权链接")
    private String connectUrl;

    @Schema(name = "任务额外属性")
    private String attr;

    @Schema(name = "任务次数上限(非会员)")
    private Integer limitCountNormal;

    @Schema(name = "任务次数上限(会员L1)")
    private Integer limitCountL1;
}
