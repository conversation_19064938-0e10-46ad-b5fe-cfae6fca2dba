/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.icon.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-09-25
**/
@Entity
@Data
@Table(name="task_icon")
public class TaskIcon implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "id")
    private String id;

    @Column(name = "`saas_id`")
    @Schema(name = "saasId")
    private String saasId;

    @Column(name = "`code`")
    @Schema(name = "code")
    private String code;

    @Column(name = "`icon`")
    @Schema(name = "icon")
    private String icon;

    public void copy(TaskIcon source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
