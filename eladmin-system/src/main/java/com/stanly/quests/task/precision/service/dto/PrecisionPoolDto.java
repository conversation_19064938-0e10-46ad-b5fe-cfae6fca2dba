/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.precision.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-03-20
**/
@Data
public class PrecisionPoolDto implements Serializable {

    /** 投放id,目前写死1 */
    private String id;

    /** 投放渠道,twitter */
    private String channel;

    /** 关联任务/活动,保留字段 */
    private String task;

    /** 所属者,edgen目前写死 */
    private String owner;

    /** distribution投放金额,3000 */
    private Double distributionAmount;

    /** contribution_ai投放金额,2100 */
    private Double contributionAiAmount;

    /** contribution_human投放金额,4200 */
    private Double contributionHumanAmount;

    /** contribution_deep_human投放金额,700 * 6 */
    private Double contributionDeepHumanAmount;

    /** distribution用户获得的最大金额,30 */
    private Double distributionMaxAmount;

    /** contribution_ai单个用户最大金额,21 */
    private Double contributionAiMaxAmount;

    /** contribution_human单个用户最大金额,42 */
    private Double contributionHumanMaxAmount;

    /** contribution_deep_human单个用户最大值,7 * 6 */
    private Double contributionDeepHumanMaxAmount;

    /** 资深用户粉丝数 */
    private Double precisionTrackCheckFollowsCount;

    /** ai最少打分数 */
    private Double precisionTrackCheckAiPoint;

    /** 资深用户aura值 */
    private Double precisionTrackCheckAsset;
}
