/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service.dto;

import com.stanly.admin.annotation.Query;
import lombok.Data;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2024-08-02
**/
@Data
public class TaskConfigQueryCriteria{

    @Query
    private String appId;

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String taskId;

    /** 模糊 */
    @Query(type = Query.Type.INNER_LIKE)
    private String title;

    /** 精确 */
    @Query
    private String status;

    /** 精确 */
    @Query
    private String code;

    /** 精确 */
    @Query(type = Query.Type.GREATER_THAN)
    private String publishTime;
}