/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.stanly.quests.task.whitelist.rest;

import com.stanly.admin.annotation.Log;
import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.whitelist.domain.TaskWhitelist;
import com.stanly.quests.task.whitelist.service.TaskWhitelistService;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistDto;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistQueryCriteria;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @website https://eladmin.vip
 * @date 2024-11-11
 **/
@RestController
@RequiredArgsConstructor
@Tag(name = "TaskWhitelistController管理")
@RequestMapping("/api/taskWhitelist")
public class TaskWhitelistController {

    private final TaskWhitelistService taskWhitelistService;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('taskWhitelist:list')")
    public void exportTaskWhitelist(HttpServletResponse response, TaskWhitelistQueryCriteria criteria) throws IOException {
        taskWhitelistService.download(taskWhitelistService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询TaskWhitelistController")
    @Operation(summary = "查询TaskWhitelistController")
    @PreAuthorize("@el.check('taskWhitelist:list')")
    public ResponseEntity<PageResult<TaskWhitelistDto>> queryTaskWhitelist(TaskWhitelistQueryCriteria criteria, Pageable pageable) {
        PageResult<TaskWhitelistDto> taskWhitelistDtoPageResult = taskWhitelistService.queryAll(criteria, pageable);
        return new ResponseEntity<>(taskWhitelistDtoPageResult, HttpStatus.OK);
    }

    @PostMapping
    @Log("新增TaskWhitelistController")
    @Operation(summary = "新增TaskWhitelistController")
    @PreAuthorize("@el.check('taskWhitelist:add')")
    public ResponseEntity<Object> createTaskWhitelist(@Validated @RequestBody TaskWhitelist resources) {
        taskWhitelistService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改TaskWhitelistController")
    @Operation(summary = "修改TaskWhitelistController")
    @PreAuthorize("@el.check('taskWhitelist:edit')")
    public ResponseEntity<Object> updateTaskWhitelist(@Validated @RequestBody TaskWhitelist resources) {
        taskWhitelistService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除TaskWhitelistController")
    @Operation(summary = "删除TaskWhitelistController")
    @PreAuthorize("@el.check('taskWhitelist:del')")
    public ResponseEntity<Object> deleteTaskWhitelist(@RequestBody String[] ids) {
        taskWhitelistService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}