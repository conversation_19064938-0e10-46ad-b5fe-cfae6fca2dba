/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.stanly.quests.task.whitelist.service;

import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.whitelist.domain.TaskWhitelist;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistDto;
import com.stanly.quests.task.whitelist.service.dto.TaskWhitelistQueryCriteria;
import org.springframework.data.domain.Pageable;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @website https://eladmin.vip
 * @description 服务接口
 * @date 2024-09-25
 **/
public interface TaskWhitelistService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    PageResult<TaskWhitelistDto> queryAll(TaskWhitelistQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<TaskWhitelistDto>
     */
    List<TaskWhitelistDto> queryAll(TaskWhitelistQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return TaskWhitelistDto
     */
    TaskWhitelistDto findById(String id);

    /**
     * 创建
     *
     * @param resources /
     */
    void create(TaskWhitelist resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(TaskWhitelist resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(String[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<TaskWhitelistDto> all, HttpServletResponse response) throws IOException;
}