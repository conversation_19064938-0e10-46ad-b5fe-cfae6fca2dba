///*
//*  Copyright 2019-2020 <PERSON>
//*
//*  Licensed under the Apache License, Version 2.0 (the "License");
//*  you may not use this file except in compliance with the License.
//*  You may obtain a copy of the License at
//*
//*  http://www.apache.org/licenses/LICENSE-2.0
//*
//*  Unless required by applicable law or agreed to in writing, software
//*  distributed under the License is distributed on an "AS IS" BASIS,
//*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//*  See the License for the specific language governing permissions and
//*  limitations under the License.
//*/
//package com.stanly.quests.task.taskCode.rest;
//
//import com.stanly.admin.annotation.Log;
//import com.stanly.admin.base.system.domain.Dict;
//import com.stanly.admin.base.system.domain.DictDetail;
//import com.stanly.admin.base.system.service.DictDetailService;
//import com.stanly.admin.base.system.service.DictService;
//import com.stanly.admin.base.system.service.dto.DictDetailDto;
//import com.stanly.admin.base.system.service.dto.DictDto;
//import com.stanly.quests.task.taskCode.domain.TaskCode;
//import com.stanly.quests.task.taskCode.service.TaskCodeService;
//import com.stanly.quests.task.taskCode.service.dto.TaskCodeQueryCriteria;
//import com.stanly.quests.task.taskCode.service.proxy.TaskCodeRemoteService;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.springframework.data.domain.Pageable;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import java.io.IOException;
//import java.util.List;
//import jakarta.servlet.http.HttpServletResponse;
//import com.stanly.admin.utils.PageResult;
//import com.stanly.quests.task.taskCode.service.dto.TaskCodeDto;
//
///**
//* @website https://eladmin.vip
//* <AUTHOR>
//* @date 2024-11-07
//**/
//@RestController
//@RequiredArgsConstructor
//@Tag(name = "任务事件配置管理")
//@RequestMapping("/api/taskCode")
//public class TaskCodeController {
//
//    private final TaskCodeService taskCodeService;
//    private final TaskCodeRemoteService taskCodeRemoteService;
//    private final DictDetailService dictDetailService;
//    private final DictService dictService;
//
//    @Log("导出数据")
//    @Operation(summary = "导出数据")
//    @GetMapping(value = "/download")
//    @PreAuthorize("@el.check('taskCode:list')")
//    public void exportTaskCode(HttpServletResponse response, TaskCodeQueryCriteria criteria) throws IOException {
//        taskCodeService.download(taskCodeService.queryAll(criteria), response);
//    }
//
//    @GetMapping
//    @Log("查询任务事件配置")
//    @Operation(summary = "查询任务事件配置")
//    @PreAuthorize("@el.check('taskCode:list')")
//    public ResponseEntity<PageResult<TaskCodeDto>> queryTaskCode(TaskCodeQueryCriteria criteria, Pageable pageable){
//        return new ResponseEntity<>(taskCodeService.queryAll(criteria,pageable),HttpStatus.OK);
//    }
//
//    @PostMapping
//    @Log("新增任务事件配置")
//    @Operation(summary = "新增任务事件配置")
//    @PreAuthorize("@el.check('taskCode:add')")
//    public ResponseEntity<Object> createTaskCode(@Validated @RequestBody TaskCode resources){
//        taskCodeService.create(resources);
//        DictDto event_code = dictService.queryByName("event_code");
//
//        DictDetail dictDetail = new DictDetail();
//        Dict dict = new Dict();
//        dict.setId(event_code.getId());
//        dictDetail.setDict(dict);
//        dictDetail.setLabel(resources.getCode());
//        dictDetail.setValue(resources.getCode());
//        dictDetail.setDictSort(1);
//        dictDetailService.create(dictDetail);
//        Boolean success = taskCodeRemoteService.SyncTaskCode(resources);
//        if(!success){
//            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
//        }
//        return new ResponseEntity<>(HttpStatus.CREATED);
//    }
//
//    @PutMapping
//    @Log("修改任务事件配置")
//    @Operation(summary = "修改任务事件配置")
//    @PreAuthorize("@el.check('taskCode:edit')")
//    public ResponseEntity<Object> updateTaskCode(@Validated @RequestBody TaskCode resources){
//        taskCodeService.update(resources);
//        DictDto event_code = dictService.queryByName("event_code");
//
//        DictDetail dictDetail = new DictDetail();
//        Dict dict = new Dict();
//        dict.setId(event_code.getId());
//        dictDetail.setDict(dict);
//        dictDetail.setLabel(resources.getCode());
//        dictDetail.setValue(resources.getCode());
//        dictDetail.setDictSort(1);
//        dictDetailService.create(dictDetail);
//        Boolean success = taskCodeRemoteService.SyncTaskCode(resources);
//        if(!success){
//            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
//        }
//        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
//    }
//
//    @DeleteMapping
//    @Log("删除任务事件配置")
//    @Operation(summary = "删除任务事件配置")
//    @PreAuthorize("@el.check('taskCode:del')")
//    public ResponseEntity<Object> deleteTaskCode(@RequestBody String[] ids) {
//        taskCodeService.deleteAll(ids);
//        return new ResponseEntity<>(HttpStatus.OK);
//    }
//}