package com.stanly.quests.task.config.service.proxy;

import com.alibaba.fastjson2.JSON;
import com.drex.activity.task.api.RemoteManageActivityConfigService;
import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.kikitrade.framework.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/8/6 15:26
 */
@Slf4j
@Service
public class TaskRemoteService {

    @DubboReference
    private RemoteManageActivityConfigService remoteTaskService;


    public boolean syncTaskConfig(TaskConfigDTO taskConfigDTO, boolean isGray) {
        log.info("taskRemoteService syncTaskConfig, taskConfigDTO:{}, isGray:{}", JSON.toJSONString(taskConfigDTO), isGray);
        Response<Boolean> response = remoteTaskService.syncTaskConfig(taskConfigDTO);
        if (response.isSuccess()) {
            return true;
        } else {
            log.error("TaskRemoteService syncTaskConfig error. taskConfigDTO:{}, isGray:{}", JSON.toJSONString(taskConfigDTO), isGray);
            return false;
        }

    }

    public boolean deleteTaskConfig(String taskId) {
        log.info("taskRemoteService deleteTaskConfig, taskId:{}", taskId);
        Response<Boolean> response = remoteTaskService.delete(taskId);
        return response.getData();
    }
}
