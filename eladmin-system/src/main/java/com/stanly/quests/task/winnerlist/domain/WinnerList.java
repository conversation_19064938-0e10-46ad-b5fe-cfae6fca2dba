package com.stanly.quests.task.winnerlist.domain;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-12-02
**/
@Data
@AllArgsConstructor
public class WinnerList implements Serializable {

    /**
     * saasId
     */
    private String saasId;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 奖池id
     */
    private String poolId;

    /**
     * 中奖者钱包地址
     */
    private String prizeAddress;

    /**
     * 中奖序号
     */
    private String prizeOrder;

}
