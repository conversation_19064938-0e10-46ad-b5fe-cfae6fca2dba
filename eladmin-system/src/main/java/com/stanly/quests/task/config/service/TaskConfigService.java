/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service;

import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.config.domain.TaskConfigManagerDTO;
import com.stanly.quests.task.config.domain.TimingPublishDTO;
import com.stanly.quests.task.config.service.dto.TaskConfigDto;
import com.stanly.quests.task.config.service.dto.TaskConfigQueryCriteria;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-08-02
**/
public interface TaskConfigService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<TaskConfigDto> queryAll(TaskConfigQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TaskConfigDto>
    */
    List<TaskConfigDto> queryAll(TaskConfigQueryCriteria criteria);

    /**
     * 根据 ID 查询
     * @param id ID
     * @return TaskConfigDto
     */
    TaskConfigDto findById(String id);

    TaskConfigDTO findByTaskId(String taskId);

    List<TaskConfigDTO> findByTaskIds(List<String> taskIds);

    TaskConfigDTO importJSON(TaskConfigDTO taskConfigDTO);

    /**
    * 创建
    * @param resources /
    */
    void create(TaskConfigManagerDTO resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(TaskConfigManagerDTO resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TaskConfigDto> all, HttpServletResponse response) throws IOException;

    /**
     * 发布任务
     * @param taskConfig /
     * @param isGray 是否灰度发布
     */
    boolean publish(TaskConfigManagerDTO taskConfig, boolean isGray);

    /**
     * 定时发布
     * @param timingPublishDTO
     */
    void timingPublish(TimingPublishDTO timingPublishDTO);
}