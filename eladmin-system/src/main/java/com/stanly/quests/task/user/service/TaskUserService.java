/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.user.service;

import com.stanly.admin.utils.PageResult;
import com.stanly.quests.task.icon.service.dto.TaskIconDto;
import com.stanly.quests.task.icon.service.dto.TaskIconQueryCriteria;
import com.stanly.quests.task.user.service.dto.TaskUserDto;
import com.stanly.quests.task.user.service.dto.TaskUserQueryCriteria;
import org.springframework.data.domain.Pageable;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-09-25
**/
public interface TaskUserService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<TaskUserDto> queryTaskUser(TaskUserQueryCriteria criteria, Pageable pageable);

}