/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.stanly.quests.task.winnerlist.service;

import com.stanly.quests.task.winnerlist.domain.WinnerList;

import java.util.List;

public interface WinnerListService {

    Boolean createWinnerList(WinnerList winnerList);

    Boolean deleteWinnerList(String saasId, String activityId, String poolId, String prizeAddress);

    List<WinnerList> getWinnerLists(String saasId, String activityId, String poolId);

}