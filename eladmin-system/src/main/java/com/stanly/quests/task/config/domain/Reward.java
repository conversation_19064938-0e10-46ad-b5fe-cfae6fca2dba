package com.stanly.quests.task.config.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/11/26 16:38
 */
@Data
public class Reward implements Serializable {

    @Schema(name = "奖励数量")
    private String rewardAmount;

    @Schema(name = "奖励数量")
    private String showRewardAmount;

    @Schema(name = "奖励币种")
    private String rewardCurrency;

    @Schema(name = "奖励专属的会员等级")
    private String rewardVipLevel;

    @Schema(name = "随机奖励的标号")
    private Integer rewardIndex;

    @Schema(name = "奖励币种")
    private String rewardType;
}
