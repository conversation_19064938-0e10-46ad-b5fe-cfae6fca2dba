///*
//*  Copyright 2019-2020 <PERSON>
//*
//*  Licensed under the Apache License, Version 2.0 (the "License");
//*  you may not use this file except in compliance with the License.
//*  You may obtain a copy of the License at
//*
//*  http://www.apache.org/licenses/LICENSE-2.0
//*
//*  Unless required by applicable law or agreed to in writing, software
//*  distributed under the License is distributed on an "AS IS" BASIS,
//*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//*  See the License for the specific language governing permissions and
//*  limitations under the License.
//*/
//package com.stanly.quests.task.user.service.impl;
//
//import com.aliyun.openservices.log.Client;
//import com.aliyun.openservices.log.common.LogContent;
//import com.aliyun.openservices.log.common.QueriedLog;
//import com.aliyun.openservices.log.exception.LogException;
//import com.aliyun.openservices.log.response.GetLogsResponse;
//import com.kikitrade.asset.api.RemoteAssetService;
//import com.kikitrade.asset.model.AssetDTO;
//import com.kikitrade.asset.model.AssetLedgerReadonlyDTO;
//import com.kikitrade.asset.model.constant.AssetBusinessType;
//import com.kikitrade.asset.model.constant.AssetCategory;
//import com.kikitrade.asset.model.constant.AssetType;
//import com.kikitrade.asset.model.request.AssetLedgersListRequest;
//import com.kikitrade.asset.model.response.AssetLaddersResponse;
//import com.kikitrade.kcustomer.api.model.CustomerBindDTO;
//import com.kikitrade.kcustomer.api.model.TCustomerDTO;
//import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
//import com.stanly.admin.exception.BadRequestException;
//import com.stanly.admin.utils.PageResult;
//import com.stanly.admin.utils.PageUtil;
//import com.stanly.quests.config.LogStoreProperties;
//import com.stanly.quests.task.user.domain.PointLedger;
//import com.stanly.quests.task.user.domain.PointLedgerList;
//import com.stanly.quests.task.user.domain.UserTrackList;
//import com.stanly.quests.task.user.service.TaskUserService;
//import com.stanly.quests.task.user.service.dto.TaskUserDto;
//import com.stanly.quests.task.user.service.dto.TaskUserQueryCriteria;
//import lombok.Builder;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.BooleanUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.domain.Pageable;
//import org.springframework.stereotype.Service;
//import org.springframework.util.CollectionUtils;
//
//import jakarta.annotation.Resource;
//import java.math.BigDecimal;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.Date;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.TimeZone;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
//* @website https://eladmin.vip
//* @description 服务实现
//* <AUTHOR>
//* @date 2024-09-25
//**/
//@Service
//@Slf4j
//@RequiredArgsConstructor
//public class TaskUserServiceImpl implements TaskUserService {
//
//    String accessId = System.getenv("aliyun_access_id");
//    String accessKey = System.getenv("aliyun_access_key");
//
//    @Resource
//    private LogStoreProperties logStoreProperties;
//
//    private Client client;
//
//    public Client getClient() {
//        if (client == null) {
//            client = new Client(logStoreProperties.getHost(), accessId, accessKey);
//        }
//        return client;
//    }
//
//    @DubboReference
//    private RemoteCustomerBindService remoteCustomerBindService;
//
//    @DubboReference
//    private RemoteAssetService remoteAssetService;
//
//
//
//    @Override
//    public PageResult<TaskUserDto> queryTaskUser(TaskUserQueryCriteria criteria, Pageable pageable){
//        log.info("TaskUserService queryTaskUser criteria: {}", criteria);
//        if (StringUtils.isNotBlank(criteria.getTwitterHandle()) || StringUtils.isNotBlank(criteria.getDiscordHandle())) {
//            if (StringUtils.isBlank(criteria.getSaasId())) {
//                throw new BadRequestException("saasId is required");
//            }
//        }
//        List<TCustomerDTO> customerDTOS = new ArrayList<>();
//        if (StringUtils.isNotBlank(criteria.getTwitterHandle())) {
//            customerDTOS = remoteCustomerBindService.findTCustomerBySocialName(criteria.getSaasId(), "twitter", criteria.getTwitterHandle());
//        } else if (StringUtils.isNotBlank(criteria.getDiscordHandle())) {
//            customerDTOS = remoteCustomerBindService.findTCustomerBySocialName(criteria.getSaasId(), "discord", criteria.getDiscordHandle());
//        }
//        if (CollectionUtils.isEmpty(customerDTOS)) {
//            return PageUtil.toPage(new ArrayList<>(), 0);
//        } else {
//            List<TaskUserDto> customers = new ArrayList<>();
//            for (TCustomerDTO customerDTO : customerDTOS) {
//                TaskUserDto customer = new TaskUserDto();
//                CustomerBindDTO customerBindDTO = remoteCustomerBindService.findById(customerDTO.getSaasId(), customerDTO.getCid());
//                BeanUtils.copyProperties(customerBindDTO, customer);
//
//                int offset = pageable.getPageNumber() * pageable.getPageSize();
//                customer.setPointLedgerList(getPointLedgers(customerDTO.getUid(), customerDTO.getSaasId(), criteria.getEventCode(), offset, pageable.getPageSize()));
//
//                // fromTime和endTime表示查询日志的时间范围，Unix时间戳格式。
//                long endTime = System.currentTimeMillis() / 1000;
//                long fromTime = endTime - 7 * 24 * 60 * 60;
//                if (Objects.nonNull(criteria.getLogFromTime()) && Objects.nonNull(criteria.getLogEndTime())) {
//                    fromTime = criteria.getLogFromTime();
//                    endTime = criteria.getLogEndTime();
//                }
//                customer.setUserTrackLists(getUserTrackData(customerDTO.getUid(), fromTime, endTime));
//                customers.add(customer);
//            }
//            return PageUtil.toPage(customers, 0);
//        }
//    }
//
//    public PointLedgerList getPointLedgers(String uid, String saasId, String eventCode, Integer offset, Integer limit) {
//        PointLedgerList pointLedgerList = new PointLedgerList();
//        AssetDTO asset = remoteAssetService.asset(uid, AssetType.POINT, AssetCategory.NORMAL);
//        pointLedgerList.setOriginalAvailable(asset.getAvailable());
//        pointLedgerList.setAvailable(add(asset.getAvailable(), saasId, uid));
//
//        if(limit != null && limit > 0){
//            AssetLedgersListRequest listRequest = new AssetLedgersListRequest();
//            listRequest.setCustomerId(uid);
//            listRequest.setAssetCategory(AssetCategory.NORMAL);
//            if (StringUtils.isNotBlank(eventCode)) {
//                listRequest.setBusinessTypes(Collections.singletonList(AssetBusinessType.valueOfByCodeDesc(eventCode)));
//            }
//            listRequest.setSaasId(saasId);
//            listRequest.setAssetType(AssetType.POINT);
//            listRequest.setOffset(offset);
//            listRequest.setLimit(limit);
//            AssetLaddersResponse response = remoteAssetService.assetLadders(listRequest);
//            List<AssetLedgerReadonlyDTO> assetLedgers = response.getAssetLedgers();
//            if (CollectionUtils.isEmpty(assetLedgers)) {
//                pointLedgerList.setPointLedgers(Collections.emptyList());
//                return pointLedgerList;
//            }
//            List<PointLedger> pointLedgers = new ArrayList<>();
//            for(AssetLedgerReadonlyDTO ledger : assetLedgers){
//                PointLedger pointLedger = new PointLedger();
//                pointLedger.setId(ledger.getId());
//                pointLedger.setAmount(ledger.getAmount());
//                pointLedger.setCreated(ledger.getCreated());
//                pointLedger.setBusinessType(AssetBusinessType.valueOf(ledger.getBusinessType()).getDesc());
//                pointLedger.setMenu(ledger.getMenu());
//                pointLedger.setDescI18n(ledger.getDescI18n());
//                pointLedger.setOperateType(ledger.getOperateType().intValue());
//                pointLedgers.add(pointLedger);
//            }
//            pointLedgerList.setPointLedgers(pointLedgers);
//            pointLedgerList.setTotal(response.getTotal());
//        }
//        return pointLedgerList;
//    }
//
//    private BigDecimal add(BigDecimal available, String saasId, String uid){
//        TCustomerDTO tCustomerDTO = remoteCustomerBindService.findTCustomerByUid(saasId, uid);
//        if(tCustomerDTO == null){
//            return available;
//        }
//        return available.add(vipAdd(available, tCustomerDTO)).add(pfpAdd(available, tCustomerDTO));
//    }
//
//    private BigDecimal vipAdd(BigDecimal originalAvailable, TCustomerDTO customer){
//        if(customer != null && BooleanUtils.isTrue(customer.getVip())){
//            return originalAvailable.multiply(new BigDecimal(0.2).multiply(new BigDecimal(customer.getVipCount())));
//        }
//        return BigDecimal.ZERO;
//    }
//
//    private BigDecimal pfpAdd(BigDecimal originalAvailable, TCustomerDTO customer){
//        if(customer.getPfpCount() == null){
//            return BigDecimal.ZERO;
//        }
//        return originalAvailable.multiply(new BigDecimal(customer.getPfpCount()));
//    }
//
//    private List<UserTrackList> getUserTrackData(String cid, long fromTime, long endTime) {
//        String query = "user_track and customer_id:" + cid;
//        List<UserTrackList> userTrackLists = new ArrayList<>();
//        Map<String, List<UserTackMsg>> map = queryLogs(query, Long.valueOf(fromTime).intValue(), Long.valueOf(endTime).intValue());
//        if(map != null && !map.isEmpty()){
//            for (Map.Entry<String, List<UserTackMsg>> stringListEntry : map.entrySet()) {
//                UserTrackList userTrackList = new UserTrackList();
//                userTrackList.setTrackId(stringListEntry.getKey());
//                userTrackList.setTrackData(stringListEntry.getValue().stream().map(UserTackMsg::toString).collect(Collectors.toList()));
//                userTrackLists.add(userTrackList);
//            }
//        }
//        return userTrackLists;
//    }
//
//    /**
//     * 通过SQL查询日志。
//     */
//    private Map<String, List<UserTackMsg>> queryLogs(String query, int fromTime, int toTime) {
//        log.info("ready to query logs from {} , query with {}", logStoreProperties.getName(), query);
//        Map<String, List<UserTackMsg>> map = new LinkedHashMap<>();
//        try {
//            for (String logStoreName : Arrays.asList(logStoreProperties.getName().split(","))) {
//                GetLogsResponse getLogsResponse = getClient().GetLogs(logStoreProperties.getProject(), logStoreName, fromTime, toTime, "", query, 200, 0, true);
//                List<QueriedLog> logs = getLogsResponse.getLogs();
//                if (CollectionUtils.isEmpty(logs)) {
//                    return map;
//                }
//                for (QueriedLog log :logs) {
//                    Map<String, LogContent> contentMap = log.mLogItem.mContents.stream().collect(Collectors.toMap(LogContent::GetKey, Function.identity()));
//                    UserTackMsg build = UserTackMsg.builder().time(convertTime(contentMap.get("time").mValue))
//                            .timeMs(contentMap.get("time_ms").mValue)
//                            .msg(StringUtils.isBlank(contentMap.get("msg").mValue) ? "" : contentMap.get("msg").mValue.replaceAll("\n", "").replaceAll("TrafficRouter.*$", ""))
//                            .build();
//                    List<UserTackMsg> traceId1 = map.getOrDefault(contentMap.get("traceId").mValue, new ArrayList<>());
//                    traceId1.add(build);
//                    traceId1.sort((o1, o2) ->
//                        o1.time.compareTo(o2.time) == 0 ? o1.timeMs.compareTo(o2.timeMs) : o1.time.compareTo(o2.time)
//                    );
//                    map.put(contentMap.get("traceId").mValue, traceId1);
//                }
//            }
//        } catch (LogException e){
//            log.error("query log exception", e);
//        }
//        return map;
//    }
//
//    private String convertTime(String mValue) {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
//        Date parse = null;
//        try {
//            parse = sdf.parse(mValue);
//            System.out.println(parse.getTime());
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        if (parse != null) {
//            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            sdf2.setTimeZone(TimeZone.getTimeZone("GMT+08:00")); // 或 "Asia/Shanghai"
//            mValue = sdf2.format(parse);
//        } else {
//            System.out.println("Failed to parse the date.");
//        }
//        return mValue;
//    }
//
//    @Builder
//    static class UserTackMsg {
//        public String time;
//        public String timeMs;
//        public String msg;
//
//        @Override
//        public String toString() {
//            return String.format("%s.%s %s", time, timeMs, msg);
//        }
//    }
//
//}