///*
//*  Copyright 2019-2020 <PERSON>
//*
//*  Licensed under the Apache License, Version 2.0 (the "License");
//*  you may not use this file except in compliance with the License.
//*  You may obtain a copy of the License at
//*
//*  http://www.apache.org/licenses/LICENSE-2.0
//*
//*  Unless required by applicable law or agreed to in writing, software
//*  distributed under the License is distributed on an "AS IS" BASIS,
//*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//*  See the License for the specific language governing permissions and
//*  limitations under the License.
//*/
//package com.stanly.quests.task.precision.rest;
//
//import com.stanly.admin.annotation.Log;
//import com.stanly.quests.task.precision.domain.PrecisionPool;
//import com.stanly.quests.task.precision.service.PrecisionPoolService;
//import com.stanly.quests.task.precision.service.dto.PrecisionPoolQueryCriteria;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.data.domain.Pageable;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import java.io.IOException;
//import com.stanly.admin.utils.PageResult;
//import com.stanly.quests.task.precision.service.dto.PrecisionPoolDto;
//
///**
//* @website https://eladmin.vip
//* <AUTHOR>
//* @date 2025-03-20
//**/
//@RestController
//@RequiredArgsConstructor
//@Tag(name = "aura投放奖池配置管理")
//@RequestMapping("/api/precisionPool")
//public class PrecisionPoolController {
//
//    private final PrecisionPoolService precisionPoolService;
//
//    @Log("导出数据")
//    @Operation(summary = "导出数据")
//    @GetMapping(value = "/download")
//    @PreAuthorize("@el.check('precisionPool:list')")
//    public void exportPrecisionPool(HttpServletResponse response, PrecisionPoolQueryCriteria criteria) throws IOException {
//        precisionPoolService.download(precisionPoolService.queryAll(criteria), response);
//    }
//
//    @GetMapping
//    @Log("查询aura投放奖池配置")
//    @Operation(summary = "查询aura投放奖池配置")
//    @PreAuthorize("@el.check('precisionPool:list')")
//    public ResponseEntity<PageResult<PrecisionPoolDto>> queryPrecisionPool(PrecisionPoolQueryCriteria criteria, Pageable pageable){
//        return new ResponseEntity<>(precisionPoolService.queryAll(criteria,pageable),HttpStatus.OK);
//    }
//
//    @PostMapping
//    @Log("新增aura投放奖池配置")
//    @Operation(summary = "新增aura投放奖池配置")
//    @PreAuthorize("@el.check('precisionPool:add')")
//    public ResponseEntity<Object> createPrecisionPool(@Validated @RequestBody PrecisionPool resources){
//        precisionPoolService.create(resources);
//        return new ResponseEntity<>(HttpStatus.CREATED);
//    }
//
//    @PutMapping
//    @Log("修改aura投放奖池配置")
//    @Operation(summary = "修改aura投放奖池配置")
//    @PreAuthorize("@el.check('precisionPool:edit')")
//    public ResponseEntity<Object> updatePrecisionPool(@Validated @RequestBody PrecisionPool resources){
//        precisionPoolService.update(resources);
//        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
//    }
//
//    @DeleteMapping
//    @Log("删除aura投放奖池配置")
//    @Operation(summary = "删除aura投放奖池配置")
//    @PreAuthorize("@el.check('precisionPool:del')")
//    public ResponseEntity<Object> deletePrecisionPool(@RequestBody String[] ids) {
//        precisionPoolService.deleteAll(ids);
//        return new ResponseEntity<>(HttpStatus.OK);
//    }
//}
