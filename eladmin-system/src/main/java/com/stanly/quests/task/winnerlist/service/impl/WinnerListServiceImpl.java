//package com.stanly.quests.task.winnerlist.service.impl;
//
//import com.kikitrade.activity.api.RemoteLotteryService;
//import com.kikitrade.activity.api.model.request.LotteryWinnersRequest;
//import com.kikitrade.activity.api.model.response.LotteryWinnersResponse;
//import com.stanly.quests.task.winnerlist.domain.WinnerList;
//import com.stanly.quests.task.winnerlist.service.WinnerListService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.Comparator;
//import java.util.List;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class WinnerListServiceImpl implements WinnerListService {
//
//    @DubboReference
//    private RemoteLotteryService remoteLotteryService;
//
//    @Override
//    public Boolean createWinnerList(WinnerList winnerList) {
//        log.info("Creating WinnerList: {}", winnerList);
//
//        LotteryWinnersRequest request = new LotteryWinnersRequest();
//        request.setSaasId(winnerList.getSaasId());
//        request.setLotteryCode(winnerList.getActivityId());
//        request.setPoolCode(winnerList.getPoolId());
//
//        LotteryWinnersRequest.Winner winner = new LotteryWinnersRequest.Winner();
//        winner.setAddress(winnerList.getPrizeAddress());
//        winner.setOrder(Integer.parseInt(winnerList.getPrizeOrder()));
//        request.setWinners(Collections.singletonList(winner));
//
//        boolean b = remoteLotteryService.saveLotteryWinners(request);
//        log.info("saveLotteryWinners result: {}", b);
//        return b;
//    }
//
//    @Override
//    public Boolean deleteWinnerList(String saasId, String activityId, String poolId, String prizeAddress) {
//        log.info("deleteWinnerList saasId:{}, activityId:{}, poolId:{}, prizeAddress:{}", saasId, activityId, poolId, prizeAddress);
//
//        LotteryWinnersRequest request = new LotteryWinnersRequest();
//        request.setSaasId(saasId);
//        request.setLotteryCode(activityId);
//        request.setPoolCode(poolId);
//
//        LotteryWinnersRequest.Winner winner = new LotteryWinnersRequest.Winner();
//        winner.setAddress(prizeAddress);
//        request.setWinners(Collections.singletonList(winner));
//
//        boolean b = remoteLotteryService.deleteLotteryWinners(request);
//        log.info("deleteWinnerList result: {}", b);
//        return b;
//    }
//
//    @Override
//    public List<WinnerList> getWinnerLists(String saasId, String activityId, String poolId) {
//        log.info("getWinnerLists, saasId: {}, activityId: {}, poolId: {}", saasId, activityId, poolId);
//        LotteryWinnersResponse response = remoteLotteryService.listLotteryWinners(saasId, activityId);
//        log.info("listLotteryWinners result: {}", response);
//
//        List<WinnerList> winnerLists = new ArrayList<>();
//        if (response != null && response.getLotteryWinners() != null) {
//            for (LotteryWinnersResponse.LotteryWinner lotteryWinners : response.getLotteryWinners()) {
//                lotteryWinners.getPoolWinners().forEach(winner -> {
//                    winnerLists.add(new WinnerList(saasId, activityId, lotteryWinners.getPoolCode(), winner.getAddress(), String.valueOf(winner.getOrder())));
//                });
//            }
//        }
//        winnerLists.sort(Comparator.comparing(WinnerList::getActivityId)
//                .thenComparing(WinnerList::getPoolId)
//                .thenComparingInt(o -> Integer.parseInt(o.getPrizeOrder())));
//        return winnerLists;
//    }
//
//}