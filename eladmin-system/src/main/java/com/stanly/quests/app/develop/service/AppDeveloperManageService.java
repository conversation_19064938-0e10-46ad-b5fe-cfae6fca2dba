/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.app.develop.service;

import com.stanly.admin.utils.PageResult;
import com.stanly.quests.app.develop.domain.AppDeveloperManage;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageDto;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2024-08-04
**/
public interface AppDeveloperManageService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<AppDeveloperManageDto> queryAll(AppDeveloperManageQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<AppDeveloperManageDto>
    */
    List<AppDeveloperManageDto> queryAll(AppDeveloperManageQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return AppDeveloperManageDto
     */
    AppDeveloperManageDto findById(String id);

    /**
    * 创建
    * @param resources /
    */
    void create(AppDeveloperManage resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(AppDeveloperManage resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<AppDeveloperManageDto> all, HttpServletResponse response) throws IOException;
}