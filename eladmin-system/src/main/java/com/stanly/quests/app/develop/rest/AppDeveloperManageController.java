/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.app.develop.rest;

import com.stanly.admin.annotation.Log;
import com.stanly.quests.app.develop.domain.AppDeveloperManage;
import com.stanly.quests.app.develop.service.AppDeveloperManageService;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageDto;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2024-08-04
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "AppDeveloperManage管理")
@RequestMapping("/api/appDeveloperManage")
public class AppDeveloperManageController {

    private final AppDeveloperManageService appDeveloperManageService;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('appDeveloperManage:list')")
    public void exportAppDeveloperManage(HttpServletResponse response, AppDeveloperManageQueryCriteria criteria) throws IOException {
        appDeveloperManageService.download(appDeveloperManageService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询AppDeveloperManage")
    @Operation(summary = "查询AppDeveloperManage")
    @PreAuthorize("@el.check('appDeveloperManage:list')")
    public ResponseEntity<PageResult<AppDeveloperManageDto>> queryAppDeveloperManage(AppDeveloperManageQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(appDeveloperManageService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增AppDeveloperManage")
    @Operation(summary = "新增AppDeveloperManage")
    @PreAuthorize("@el.check('appDeveloperManage:add')")
    public ResponseEntity<Object> createAppDeveloperManage(@Validated @RequestBody AppDeveloperManage resources){
        appDeveloperManageService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改AppDeveloperManage")
    @Operation(summary = "修改AppDeveloperManage")
    @PreAuthorize("@el.check('appDeveloperManage:edit')")
    public ResponseEntity<Object> updateAppDeveloperManage(@Validated @RequestBody AppDeveloperManage resources){
        appDeveloperManageService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除AppDeveloperManage")
    @Operation(summary = "删除AppDeveloperManage")
    @PreAuthorize("@el.check('appDeveloperManage:del')")
    public ResponseEntity<Object> deleteAppDeveloperManage(@RequestBody String[] ids) {
        appDeveloperManageService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}