/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.app.develop.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-08-04
**/
@Entity
@Data
@Table(name="app_developer_manage")
public class AppDeveloperManage implements Serializable {

    @Column(name = "`app`",nullable = false)
    @NotBlank
    @Schema(name = "app")
    private String app;

    @Column(name = "`api_key`")
    @Schema(name = "api_key")
    private String apiKey;

    @Column(name = "`api_secret`")
    @Schema(name = "api_secret")
    private String apiSecret;

    @Column(name = "`client_id`")
    @Schema(name = "client_id")
    private String clientId;

    @Column(name = "`client_secret`")
    @Schema(name = "client_secret")
    private String clientSecret;

    @Column(name = "`bearer_token`")
    @Schema(name = "bearer_token")
    private String bearerToken;

    @Id
    @Column(name = "`id`")
    @Schema(name = "id")
    private String id;

    public void copy(AppDeveloperManage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
