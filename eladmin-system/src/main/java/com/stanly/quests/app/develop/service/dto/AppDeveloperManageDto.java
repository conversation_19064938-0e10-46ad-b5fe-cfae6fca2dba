/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.app.develop.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-08-04
**/
@Data
public class AppDeveloperManageDto implements Serializable {

    /** app */
    private String app;

    /** api_key */
    private String apiKey;

    /** api_secret */
    private String apiSecret;

    /** client_id */
    private String clientId;

    /** client_secret */
    private String clientSecret;

    /** bearer_token */
    private String bearerToken;

    /** id */
    private String id;
}