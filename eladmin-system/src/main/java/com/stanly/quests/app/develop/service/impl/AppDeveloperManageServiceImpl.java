/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.app.develop.service.impl;

import com.stanly.admin.utils.FileUtil;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.quests.app.develop.domain.AppDeveloperManage;
import lombok.RequiredArgsConstructor;
import com.stanly.quests.app.develop.repository.AppDeveloperManageRepository;
import com.stanly.quests.app.develop.service.AppDeveloperManageService;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageDto;
import com.stanly.quests.app.develop.service.dto.AppDeveloperManageQueryCriteria;
import com.stanly.quests.app.develop.service.mapstruct.AppDeveloperManageMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-08-04
**/
@Service
@RequiredArgsConstructor
public class AppDeveloperManageServiceImpl implements AppDeveloperManageService {

    private final AppDeveloperManageRepository appDeveloperManageRepository;
    private final AppDeveloperManageMapper appDeveloperManageMapper;

    @Override
    public PageResult<AppDeveloperManageDto> queryAll(AppDeveloperManageQueryCriteria criteria, Pageable pageable){
        Page<AppDeveloperManage> page = appDeveloperManageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(appDeveloperManageMapper::toDto));
    }

    @Override
    public List<AppDeveloperManageDto> queryAll(AppDeveloperManageQueryCriteria criteria){
        return appDeveloperManageMapper.toDto(appDeveloperManageRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public AppDeveloperManageDto findById(String id) {
        AppDeveloperManage appDeveloperManage = appDeveloperManageRepository.findById(id).orElseGet(AppDeveloperManage::new);
        ValidationUtil.isNull(appDeveloperManage.getId(),"AppDeveloperManage","id",id);
        return appDeveloperManageMapper.toDto(appDeveloperManage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(AppDeveloperManage resources) {
        resources.setId(IdUtil.simpleUUID()); 
        appDeveloperManageRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AppDeveloperManage resources) {
        AppDeveloperManage appDeveloperManage = appDeveloperManageRepository.findById(resources.getId()).orElseGet(AppDeveloperManage::new);
        ValidationUtil.isNull( appDeveloperManage.getId(),"AppDeveloperManage","id",resources.getId());
        appDeveloperManage.copy(resources);
        appDeveloperManageRepository.save(appDeveloperManage);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            appDeveloperManageRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<AppDeveloperManageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AppDeveloperManageDto appDeveloperManage : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("app", appDeveloperManage.getApp());
            map.put("api_key", appDeveloperManage.getApiKey());
            map.put("api_secret", appDeveloperManage.getApiSecret());
            map.put("client_id", appDeveloperManage.getClientId());
            map.put("client_secret", appDeveloperManage.getClientSecret());
            map.put("bearer_token", appDeveloperManage.getBearerToken());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}