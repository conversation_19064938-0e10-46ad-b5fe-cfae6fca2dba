/*
 * Copyright 2019-2020 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.stanly.admin.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * URL末尾斜杠处理过滤器
 * 当请求URL以斜杠结尾时，截断斜杠
 *
 * <AUTHOR>
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class UrlTrailingSlashFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(UrlTrailingSlashFilter.class);

    @Override
    public void init(FilterConfig filterConfig) {
        log.info("初始化URL末尾斜杠处理过滤器");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String requestURI = httpRequest.getRequestURI();
        
        // 检查URL是否以斜杠结尾，且长度大于1（排除根路径"/"）
        if (requestURI.length() > 1 && requestURI.endsWith("/")) {
            // 创建一个新的请求包装器，修改请求URI
            HttpServletRequest wrappedRequest = new HttpServletRequestWrapper(httpRequest) {
                @Override
                public String getRequestURI() {
                    return requestURI.substring(0, requestURI.length() - 1);
                }
                
                @Override
                public StringBuffer getRequestURL() {
                    StringBuffer url = new StringBuffer(super.getRequestURL());
                    if (url.length() > 0 && url.charAt(url.length() - 1) == '/') {
                        url.deleteCharAt(url.length() - 1);
                    }
                    return url;
                }
            };
            log.debug("URL末尾斜杠已截断: {} -> {}", requestURI, wrappedRequest.getRequestURI());
            chain.doFilter(wrappedRequest, response);
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        log.info("销毁URL末尾斜杠处理过滤器");
    }
}