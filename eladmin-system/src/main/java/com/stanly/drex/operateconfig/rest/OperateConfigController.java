package com.stanly.drex.operateconfig.rest;

import com.stanly.drex.operateconfig.domain.OperateConfig;
import com.stanly.drex.operateconfig.service.OperateConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Tag(name = "OperateConfigController管理")
@RequestMapping("/api/operateconfig")
public class OperateConfigController {

    private final OperateConfigService operateConfigService;

    @GetMapping
    public List<OperateConfig> getWinnerLists(String projectComponent, String paramterKey) {
        return operateConfigService.getOperateConfigs(projectComponent, paramterKey);
    }

    @PostMapping(value = "/refresh")
    public Boolean refreshWinnerList(@RequestBody OperateConfig operateConfig) {
        return operateConfigService.refreshOperateConfig(operateConfig);
    }

}