package com.stanly.drex.operateconfig.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperateConfig implements Serializable {

    /**
     * 项目组件
     */
    private String projectComponent;

    /**
     * 参数名称
     */
    private String paramterKey;

    /**
     * 参数值
     */
    private String paramterValue;

}
