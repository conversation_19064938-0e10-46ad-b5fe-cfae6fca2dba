package com.stanly.drex.operateconfig.service.impl;

import com.drex.core.api.RemoteManageService;
import com.drex.core.api.response.ManageResponse;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.operateconfig.domain.OperateConfig;
import com.stanly.drex.operateconfig.service.OperateConfigService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class OperateConfigServiceImpl implements OperateConfigService {

    @DubboReference
    private RemoteManageService remoteManageService;

    private static final Map<String, List<String>> allowOperateConfigKeys = new HashMap<>();

    @PostConstruct
    public void init() {
        List<String> coreKeys = new ArrayList<>();
        coreKeys.add("core.x-id");
        coreKeys.add("core.x-handle-name");
        coreKeys.add("core.x-text-length");
        coreKeys.add("core.youtube-id");
        coreKeys.add("core.youtube-duration");
        allowOperateConfigKeys.put("drex-core", coreKeys);
    }

    @Override
    public List<OperateConfig> getOperateConfigs(String projectComponent, String paramterKey) {
        log.info("getOperateConfigs: {}, {}", projectComponent, paramterKey);
        if (!allowOperateConfigKeys.containsKey(projectComponent)) {
            log.error("not in allow component {}", projectComponent);
            return new ArrayList<>();
        }
        List<String> keys = allowOperateConfigKeys.get(projectComponent);
        List<String> queryKeys = new ArrayList<>(keys);
        if (StringUtils.isNotBlank(paramterKey)) {
            if(!keys.contains(paramterKey)){
                log.error("not in allow key {}", paramterKey);
                return new ArrayList<>();
            }
            queryKeys.clear();
            queryKeys.add(paramterKey);
        }

        Response<List<com.drex.core.api.request.OperateConfig>> operateConfigs = remoteManageService.getOperateConfigs(queryKeys);
        if(operateConfigs.isSuccess() && !CollectionUtils.isEmpty(operateConfigs.getData())){
            return operateConfigs.getData().stream().map(operateConfig -> {
                OperateConfig config = new OperateConfig();
                config.setProjectComponent(projectComponent);
                config.setParamterKey(operateConfig.getParamterKey());
                config.setParamterValue(operateConfig.getParamterValue());
                return config;
            }).toList();
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean refreshOperateConfig(OperateConfig operateConfig) {
        log.info("refreshOperateConfig: {}", operateConfig);
        if (!allowOperateConfigKeys.containsKey(operateConfig.getProjectComponent())) {
            log.error("not allow operate project component config {}", operateConfig.getProjectComponent());
            return false;
        }
        List<String> keys = allowOperateConfigKeys.get(operateConfig.getProjectComponent());
        if(!keys.contains(operateConfig.getParamterKey())){
            log.error("not allow operate parameter config key {}", operateConfig.getParamterKey());
            return false;
        }

        com.drex.core.api.request.OperateConfig config = new com.drex.core.api.request.OperateConfig();
        config.setParamterKey(operateConfig.getParamterKey());
        config.setParamterValue(operateConfig.getParamterValue());
        Response<ManageResponse> manageResponseResponse = remoteManageService.refreshOperateConfig(config);
        log.info("refreshOperateConfig: {}", manageResponseResponse);
        return manageResponseResponse.isSuccess() && manageResponseResponse.getData().isSuccess();
    }

}