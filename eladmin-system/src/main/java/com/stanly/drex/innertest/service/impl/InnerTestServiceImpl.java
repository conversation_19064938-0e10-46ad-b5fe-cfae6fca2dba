package com.stanly.drex.innertest.service.impl;

import com.drex.core.api.RemoteManageService;
import com.kikitrade.framework.common.model.Response;
import com.stanly.admin.utils.RedisUtils;
import com.stanly.drex.innertest.service.InnerTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class InnerTestServiceImpl implements InnerTestService {

    private final RedisUtils redisUtils;

    @DubboReference
    private RemoteManageService remoteManageService;

    // Redis中存储内测用户列表的key
    private static final String INNER_TEST_USERS_KEY = "inner_test_users";

    @Override
    public Boolean innerTestUserUpload(List<String> userIds) {
        log.info("innerTestUserUpload userIds {}", userIds);

        try {
            Response<Boolean> addResponse = remoteManageService.addInnerTestUsers(userIds);
            if (addResponse.isSuccess() && addResponse.getData()) {
                // 将用户列表保存到Redis中
                // 先清除之前的数据
                redisUtils.del(INNER_TEST_USERS_KEY);

                // 将 List<String> 转换为 List<Object>
                List<Object> userIdsObj = new ArrayList<>(userIds);

                // 保存新的用户列表
                redisUtils.lSet(INNER_TEST_USERS_KEY, userIdsObj);

                log.info("成功将{}个内测用户保存到Redis中", userIds.size());
                return true;
            }
        } catch (Exception e) {
            log.error("保存内测用户到Redis失败", e);
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> exportInnerTestUsers() {
        log.info("开始导出内测用户数据");

        // 从Redis中获取内测用户列表
        List<Object> userIdsFromRedis = redisUtils.lGet(INNER_TEST_USERS_KEY, 0, -1);
        List<String> userIds = new ArrayList<>();

        if (userIdsFromRedis != null && !userIdsFromRedis.isEmpty()) {
            // 将Object类型转换为String类型
            for (Object userId : userIdsFromRedis) {
                userIds.add(userId.toString());
            }
            log.info("从Redis中获取到{}个内测用户", userIds.size());
        }

        // 准备导出数据
        List<Map<String, Object>> list = new ArrayList<>();

        // 添加表头行
        Map<String, Object> headerMap = new LinkedHashMap<>();
        headerMap.put("customer_id", "customer_id");
        list.add(headerMap);

        // 添加数据行
        for (String userId : userIds) {
            Map<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("customer_id", userId);
            list.add(dataMap);
        }
        return list;
    }
}