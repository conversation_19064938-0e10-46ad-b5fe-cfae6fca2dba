package com.stanly.drex.innertest.rest;

import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import com.stanly.admin.annotation.Log;

import com.stanly.drex.innertest.service.InnerTestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.stanly.admin.annotation.AnonymousAccess;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.stanly.admin.utils.CsvUtil;

@RestController
@RequiredArgsConstructor
@Tag(name = "内测管理")
@RequestMapping("/api/innerTest")
@Slf4j
public class InnerTestController {

    @Resource
    private InnerTestService innerTestService;

    @Log("上传内测用户")
    @Operation(summary = "上传内测用户")
    @PostMapping(value = "/upload")
    @PreAuthorize("@el.check('innerTest:add')")
    public ResponseEntity<Object> innerTestUserUpload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return new ResponseEntity<>("请选择文件", HttpStatus.BAD_REQUEST);
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.endsWith(".csv")) {
            return new ResponseEntity<>("请上传CSV文件", HttpStatus.BAD_REQUEST);
        }
        List<String> userIds = new ArrayList<>();
        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream()))) {
            // 读取并忽略第一行（标题行）
            String[] header = reader.readNext();
            if (header == null) {
                return new ResponseEntity<>("CSV文件为空", HttpStatus.BAD_REQUEST);
            }

            log.info("CSV文件标题行: {}", String.join(", ", header));

            // 读取剩余行
            String[] line;
            int lineNumber = 1; // 从第二行开始（索引为1）
            while ((line = reader.readNext()) != null) {
                if (line.length > 0 && line[0] != null && !line[0].trim().isEmpty()) {
                    String userId = line[0].trim();
                    userIds.add(userId);
                    log.info("第{}行数据: {}", lineNumber + 1, userId);
                }
                lineNumber++;
            }

            if (userIds.isEmpty()) {
                return new ResponseEntity<>("CSV文件中没有有效的用户ID", HttpStatus.BAD_REQUEST);
            }

            Boolean uploadResult = innerTestService.innerTestUserUpload(userIds);
            log.info("innerTestUserUpload uploadResult {}", uploadResult);

            if (uploadResult) {
                return new ResponseEntity<>("成功解析CSV文件并保存到Redis，共" + userIds.size() + "个用户", HttpStatus.OK);
            } else {
                return new ResponseEntity<>("解析CSV文件成功，但保存到Redis失败", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        } catch (IOException e) {
            log.error("读取CSV文件失败", e);
            return new ResponseEntity<>("读取CSV文件失败: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        } catch (CsvValidationException e) {
            log.error("CSV文件格式错误", e);
            return new ResponseEntity<>("CSV文件格式错误: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @Log("导出内测用户数据")
    @Operation(summary = "导出内测用户数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('innerTest:list')")
    public void exportInnerTestUsers(HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = innerTestService.exportInnerTestUsers();
        // 将数据导出为 CSV 文件，不包含重复的表头
        CsvUtil.downloadCsv(list, response, false);
        log.info("导出内测用户数据完成，共{}条数据", list.size());
    }
}