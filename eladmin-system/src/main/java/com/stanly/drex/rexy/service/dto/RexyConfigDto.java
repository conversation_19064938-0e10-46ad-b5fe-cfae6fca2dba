/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.rexy.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-22
**/
@Data
public class RexyConfigDto implements Serializable {

    /** ID */
    private String id;

    /** 名称 */
    private String name;

    /** 等级 */
    private String level;

    /** 生产速率 */
    private Integer rate;

    /** 篮子上限 */
    private Integer limit;

    /** 是否为默认 */
    private Boolean isDefault;

    /** 头像 */
    private String avatar;

    /** 生效时间 */
    private Long effectiveTime;

    /** 过期时间 */
    private Long expirationTime;

    /** 列表小图 */
    private String miniAvatar;

    /** 头像 */
    private String circleAvatar;
}
