/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.rexy.rest;

import com.aliyun.oss.model.PutObjectResult;
import com.stanly.admin.annotation.Log;
import com.stanly.admin.utils.FileUtil;
import com.stanly.admin.utils.UploadAliyunOssUtil;
import com.stanly.drex.rexy.domain.RexyConfig;
import com.stanly.drex.rexy.service.RexyConfigService;
import com.stanly.drex.rexy.service.dto.RexyConfigQueryCriteria;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.File;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;
import com.stanly.drex.rexy.service.dto.RexyConfigDto;
import org.springframework.web.multipart.MultipartFile;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-05-22
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "RexyConfigController管理")
@RequestMapping("/api/rexyConfig")
public class RexyConfigController {

    private final RexyConfigService rexyConfigService;
    private final UploadAliyunOssUtil uploadAliyunOssUtil;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('rexyConfig:list')")
    public void exportRexyConfig(HttpServletResponse response, RexyConfigQueryCriteria criteria) throws IOException {
        rexyConfigService.download(rexyConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询RexyConfigController")
    @Operation(summary = "查询RexyConfigController")
    @PreAuthorize("@el.check('rexyConfig:list')")
    public ResponseEntity<PageResult<RexyConfigDto>> queryRexyConfig(RexyConfigQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(rexyConfigService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增RexyConfigController")
    @Operation(summary = "新增RexyConfigController")
    @PreAuthorize("@el.check('rexyConfig:add')")
    public ResponseEntity<Object> createRexyConfig(@Validated @RequestBody RexyConfig resources){
        rexyConfigService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改RexyConfigController")
    @Operation(summary = "修改RexyConfigController")
    @PreAuthorize("@el.check('rexyConfig:edit')")
    public ResponseEntity<Object> updateRexyConfig(@Validated @RequestBody RexyConfig resources){
        rexyConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除RexyConfigController")
    @Operation(summary = "删除RexyConfigController")
    @PreAuthorize("@el.check('rexyConfig:del')")
    public ResponseEntity<Object> deleteRexyConfig(@RequestBody String[] ids) {
        rexyConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "upload")
    @Log("上传项目图片")
    @Operation(summary = "上传项目图片")
    public ResponseEntity<Object> uploadImage(MultipartFile file, HttpServletRequest request){
        File f = FileUtil.toFile(file);
        String fileName = f.getName();
        PutObjectResult result = uploadAliyunOssUtil.putObject("drex/avatar", fileName, f);
        return ResponseEntity.ok(uploadAliyunOssUtil.getLocation("drex/avatar", fileName));
    }
}
