/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.rexy.service;

import com.stanly.drex.rexy.domain.RexyConfig;
import com.stanly.drex.rexy.service.dto.RexyConfigDto;
import com.stanly.drex.rexy.service.dto.RexyConfigQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-05-22
**/
public interface RexyConfigService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<RexyConfigDto> queryAll(RexyConfigQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<RexyConfigDto>
    */
    List<RexyConfigDto> queryAll(RexyConfigQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return RexyConfigDto
     */
    RexyConfigDto findById(String id);

    /**
    * 创建
    * @param resources /
    */
    void create(RexyConfig resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(RexyConfig resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<RexyConfigDto> all, HttpServletResponse response) throws IOException;
}