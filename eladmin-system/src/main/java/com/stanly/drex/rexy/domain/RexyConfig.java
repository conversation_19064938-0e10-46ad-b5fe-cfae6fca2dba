/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.rexy.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-22
**/
@Entity
@Data
@Table(name="rexy_config")
public class RexyConfig implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "ID")
    private String id;

    @Column(name = "`name`")
    @Schema(name = "名称")
    private String name;

    @Column(name = "`level`")
    @Schema(name = "等级")
    private String level;

    @Column(name = "`rate`")
    @Schema(name = "生产速率")
    private Integer rate;

    @Column(name = "`limit`")
    @Schema(name = "篮子上限")
    private Integer limit;

    @Column(name = "`is_default`")
    @Schema(name = "是否为默认")
    private Boolean isDefault;

    @Column(name = "`avatar`")
    @Schema(name = "头像")
    private String avatar;

    @Column(name = "`effective_time`")
    @Schema(name = "生效时间")
    private Long effectiveTime;

    @Column(name = "`expiration_time`")
    @Schema(name = "过期时间")
    private Long expirationTime;

    @Column(name = "`mini_avatar`")
    @Schema(name = "列表小图")
    private String miniAvatar;

    @Column(name = "`circle_avatar`")
    @Schema(name = "头像")
    private String circleAvatar;

    public void copy(RexyConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
