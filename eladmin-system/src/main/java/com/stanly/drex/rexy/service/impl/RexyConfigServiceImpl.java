/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.rexy.service.impl;

import com.drex.core.api.RemoteRexyService;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.request.RexyConfigDTO;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.rexy.domain.RexyConfig;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.admin.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.stanly.drex.rexy.repository.RexyConfigRepository;
import com.stanly.drex.rexy.service.RexyConfigService;
import com.stanly.drex.rexy.service.dto.RexyConfigDto;
import com.stanly.drex.rexy.service.dto.RexyConfigQueryCriteria;
import com.stanly.drex.rexy.service.mapstruct.RexyConfigMapper;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-05-16
**/
@Service
@RequiredArgsConstructor
public class RexyConfigServiceImpl implements RexyConfigService {

    private final RexyConfigRepository rexyConfigRepository;
    private final RexyConfigMapper rexyConfigMapper;
    @DubboReference
    private RemoteRexyService remoteRexyService;

    @Override
    public PageResult<RexyConfigDto> queryAll(RexyConfigQueryCriteria criteria, Pageable pageable){
        Page<RexyConfig> page = rexyConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(rexyConfigMapper::toDto));
    }

    @Override
    public List<RexyConfigDto> queryAll(RexyConfigQueryCriteria criteria){
        return rexyConfigMapper.toDto(rexyConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public RexyConfigDto findById(String id) {
        RexyConfig rexyConfig = rexyConfigRepository.findById(id).orElseGet(RexyConfig::new);
        ValidationUtil.isNull(rexyConfig.getId(),"RexyConfig","id",id);
        return rexyConfigMapper.toDto(rexyConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(RexyConfig resources) {
        resources.setId(IdUtil.getSnowflakeNextIdStr());
        rexyConfigRepository.save(resources);
        RexyConfigDTO configDTO = new RexyConfigDTO();
        try {
            BeanUtils.copyProperties(configDTO, resources);
            Response response = remoteRexyService.addRexy(configDTO);
            if(!response.isSuccess()){
                throw new CoreException();
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {

            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(RexyConfig resources) {
        RexyConfig rexyConfig = rexyConfigRepository.findById(resources.getId()).orElseGet(RexyConfig::new);
        ValidationUtil.isNull( rexyConfig.getId(),"RexyConfig","id",resources.getId());
        rexyConfig.copy(resources);
        rexyConfigRepository.save(rexyConfig);
        RexyConfigDTO configDTO = new RexyConfigDTO();
        try {
            BeanUtils.copyProperties(configDTO, rexyConfig);
            Response response = remoteRexyService.addRexy(configDTO);
            if(!response.isSuccess()){
                throw new CoreException();
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            rexyConfigRepository.deleteById(id);
            remoteRexyService.removeRexy(id);
        }
    }

    @Override
    public void download(List<RexyConfigDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (RexyConfigDto rexyConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("名称", rexyConfig.getName());
            map.put("等级", rexyConfig.getLevel());
            map.put("生产速率", rexyConfig.getRate());
            map.put("篮子上限", rexyConfig.getLimit());
            map.put("是否为默认", rexyConfig.getIsDefault());
            map.put("头像", rexyConfig.getAvatar());
            map.put("生效时间", rexyConfig.getEffectiveTime());
            map.put("过期时间", rexyConfig.getExpirationTime());
            map.put("列表小图", rexyConfig.getMiniAvatar());
            map.put("头像", rexyConfig.getCircleAvatar());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}
