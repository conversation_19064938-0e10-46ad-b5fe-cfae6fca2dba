package com.stanly.drex.videoriskconfig.rest;

import com.drex.core.api.response.RiskConfig;
import com.stanly.drex.videoriskconfig.service.VideoRiskConfigService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Tag(name = "VideoRiskConfigController管理")
@RequestMapping("/api/videoriskconfig")
public class VideoRiskConfigController {

    private final VideoRiskConfigService videoRiskConfigService;

    @GetMapping
    public RiskConfig getVideoRiskLists() {
        return videoRiskConfigService.getVideoRiskLists();
    }

    @PostMapping(value = "/refresh")
    public Boolean refreshVideoRiskConfig(@RequestBody RiskConfig videoRiskConfig) {
        return videoRiskConfigService.refreshVideoRiskConfig(videoRiskConfig);
    }

}