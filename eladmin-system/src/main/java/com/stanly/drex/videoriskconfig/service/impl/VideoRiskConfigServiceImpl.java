package com.stanly.drex.videoriskconfig.service.impl;

import com.drex.core.api.RemoteManageService;
import com.drex.core.api.response.IndicatorConfig;
import com.drex.core.api.response.RiskConfig;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.videoriskconfig.service.VideoRiskConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class VideoRiskConfigServiceImpl implements VideoRiskConfigService {

    @DubboReference
    private RemoteManageService remoteManageService;

    @Override
    public RiskConfig getVideoRiskLists() {
        Response<RiskConfig> videoRiskConfigs = remoteManageService.getVideoRiskConfigs();
        if(videoRiskConfigs.isSuccess() && Objects.nonNull(videoRiskConfigs.getData())){
            return videoRiskConfigs.getData();
        }
        return null;
    }

    @Override
    public Boolean refreshVideoRiskConfig(RiskConfig riskConfig) {
        log.info("refreshVideoRiskConfig riskConfig : {}", riskConfig);

        IndicatorConfig videoIndicatorConfig = riskConfig.getIndicatorConfig();
        
        // 校验 VideoIndicatorConfig 的每一个指标权重之和不能大于 1
        if (videoIndicatorConfig != null) {
            double totalWeight = 0.0;
            
            // 累加所有指标的权重
            totalWeight += videoIndicatorConfig.getRepeatedEvent().getWeight();
            totalWeight += videoIndicatorConfig.getCompletion().getWeight();
            totalWeight += videoIndicatorConfig.getFocus().getWeight();
            totalWeight += videoIndicatorConfig.getIdle().getWeight();
            totalWeight += videoIndicatorConfig.getEnvironment().getWeight();
            totalWeight += videoIndicatorConfig.getTimestamp().getWeight();
            totalWeight += videoIndicatorConfig.getPlaybackSpeed().getWeight();
            totalWeight += videoIndicatorConfig.getAbnormalSeek().getWeight();
            totalWeight += videoIndicatorConfig.getFingerprintDuplication().getWeight();
            totalWeight += videoIndicatorConfig.getMaliciousIp().getWeight();
            totalWeight += videoIndicatorConfig.getEventOrder().getWeight();
            
            // 检查权重和是否超过1.0（考虑浮点数精度问题）
            if (Math.abs(totalWeight - 1.0) > 0.000001) {
                log.error("The sum of all weights must be exactly 1.0, but was: {}", totalWeight);
                throw new IllegalArgumentException("所有指标权重的总和必须等于1.0，当前总和为：" + totalWeight);
            }
        }
        
        Response<Boolean> response = remoteManageService.refreshVideoRiskConfigs(riskConfig);
        log.info("refreshVideoRiskConfig: {}", response);
        return response.isSuccess() && response.getData();
    }
}