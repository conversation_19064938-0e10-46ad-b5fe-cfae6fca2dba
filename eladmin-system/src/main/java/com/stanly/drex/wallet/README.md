# 钱包和社媒绑定管理模块

## 模块结构

```
wallet/
├── dto/                           # 数据传输对象
│   ├── WalletBindingInfo.java     # 钱包绑定信息
│   ├── WalletBindingQueryRequest.java   # 钱包查询请求
│   ├── WalletBindingQueryResponse.java  # 钱包查询响应
│   ├── WalletUnbindRequest.java   # 钱包解绑请求
│   ├── SocialBindingInfo.java     # 社媒绑定信息
│   ├── SocialBindingQueryRequest.java   # 社媒查询请求
│   ├── SocialBindingQueryResponse.java  # 社媒查询响应
│   └── SocialUnbindRequest.java   # 社媒解绑请求
├── rest/                          # 控制器层
│   └── WalletBindingController.java     # 钱包和社媒绑定控制器
├── service/                       # 服务层
│   ├── WalletBindingService.java  # 服务接口
│   └── impl/
│       └── WalletBindingServiceImpl.java # 服务实现
└── README.md                      # 说明文档
```

## API 接口

### 1. 查询钱包绑定关系

**接口地址：** `POST /api/wallet/binding/query`

**请求参数：**
```json
{
  "userId": "用户ID",
  "walletAddress": "钱包地址",
  "walletType": "钱包类型",
  "page": 1,
  "size": 10
}
```

**响应数据：**
```json
{
  "total": 100,
  "page": 1,
  "size": 10,
  "bindings": [
    {
      "bindingId": "绑定ID",
      "userId": "用户ID",
      "username": "用户名",
      "walletAddress": "钱包地址",
      "walletType": "钱包类型",
      "status": "绑定状态",
      "bindTime": *************,
      "lastActiveTime": *************,
      "remark": "备注"
    }
  ]
}
```

### 2. 解绑钱包

**接口地址：** `POST /api/wallet/unbind`

**请求参数：**
```json
{
  "userId": "用户ID",
  "walletAddress": "钱包地址",
  "walletType": "钱包类型",
  "reason": "解绑原因"
}
```

**响应数据：**
```json
true/false
```

### 3. 查询社媒绑定关系

**接口地址：** `POST /api/social/binding/query`

**请求参数：**
```json
{
  "userId": "用户ID",
  "socialType": "社媒平台类型",
  "socialAccount": "社媒账号",
  "page": 1,
  "size": 10
}
```

**响应数据：**
```json
{
  "total": 100,
  "page": 1,
  "size": 10,
  "bindings": [
    {
      "bindingId": "绑定ID",
      "userId": "用户ID",
      "username": "用户名",
      "passportId": "PassportId",
      "socialType": "社媒平台类型",
      "socialAccount": "社媒账号",
      "socialUsername": "社媒用户名",
      "status": "绑定状态",
      "bindTime": *************,
      "lastActiveTime": *************,
      "remark": "备注"
    }
  ]
}
```

### 4. 解绑社媒

**接口地址：** `POST /api/social/unbind`

**请求参数：**
```json
{
  "userId": "用户ID",
  "socialType": "社媒平台类型",
  "socialAccount": "社媒账号",
  "bindingId": "绑定ID",
  "reason": "解绑原因"
}
```

**响应数据：**
```json
true/false
```

## 依赖说明

### Dubbo 服务依赖

本模块依赖以下 Dubbo 服务：

#### 1. RemotePassportService

用于查询用户的 passportId 和钱包相关操作：

```java
public interface RemotePassportService {

    /**
     * 根据用户名查询 Passport 信息
     */
    Response<PassportDTO> getByHandleName(String handleName);

    /**
     * 查询 Passport 连接信息
     */
    Response<List<PassportConnectDTO>> getPassportConnect(String passportId);

    /**
     * 解绑钱包
     */
    Response<Boolean> unbindWallet(UnbindWalletRequest request);
}
```

#### 2. RemoteCustomerBindService

用于社媒绑定相关操作：

```java
public interface RemoteCustomerBindService {

    /**
     * 根据客户ID查询绑定信息
     */
    Response<List<SocialBindingInfo>> findByCustomerId(String customerId);

    /**
     * 解绑社媒
     */
    Response<Boolean> unbindSocial(String passportId, String socialType, String socialAccount, String bindingId);
}
```

## 业务流程

### 社媒绑定查询流程

1. 接收前端查询请求（包含用户ID等参数）
2. 调用 `RemotePassportService.getByHandleName()` 查询用户的 passportId
3. 调用 `RemoteCustomerBindService.findByCustomerId()` 查询绑定信息
4. 根据请求参数过滤结果并返回

### 社媒解绑流程

1. 接收前端解绑请求（包含用户ID、社媒类型、账号等）
2. 调用 `RemotePassportService.getByHandleName()` 查询用户的 passportId
3. 调用 `RemoteCustomerBindService.unbindSocial()` 执行解绑操作
4. 返回解绑结果

## 使用说明

1. 确保 `RemotePassportService` 和 `RemoteCustomerBindService` 服务已正确配置并可用
2. 确保相关的 DTO 类在服务提供方也有对应的定义
3. 根据实际业务需求调整 DTO 字段和验证规则
4. 可以根据需要添加权限控制注解（如 `@PreAuthorize`）

## 注意事项

1. 社媒绑定功能采用两步调用的方式：先查询 passportId，再进行具体操作
2. 所有的业务逻辑都通过 Dubbo 调用远程服务实现
3. 本模块只负责接口层的数据转换和异常处理
4. 日志记录包含请求参数和响应结果，便于问题排查
5. 异常处理确保接口的稳定性，避免因远程服务异常导致的系统崩溃
6. 支持根据社媒类型和账号进行过滤查询
