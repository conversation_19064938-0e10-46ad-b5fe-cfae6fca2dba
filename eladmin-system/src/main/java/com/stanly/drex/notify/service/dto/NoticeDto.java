/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.notify.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-13
**/
@Data
public class NoticeDto implements Serializable {

    /** 消息id */
    private String id;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 通知内容 */
    private String content;

    /** 跳转链接 */
    private String link;

    /** 通知时间 */
    private Long notifyTime;

    /** 通知状态 */
    private String status;

    /** 目标用户 */
    private Integer sendTo;
}