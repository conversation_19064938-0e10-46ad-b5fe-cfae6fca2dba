/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.notify.service.impl;

import com.drex.core.api.RemoteInformationService;
import com.drex.core.api.RemoteManageService;
import com.drex.core.api.request.NoticeDTO;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.notify.domain.Notice;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.admin.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.stanly.drex.notify.repository.NoticeRepository;
import com.stanly.drex.notify.service.NoticeService;
import com.stanly.drex.notify.service.dto.NoticeDto;
import com.stanly.drex.notify.service.dto.NoticeQueryCriteria;
import com.stanly.drex.notify.service.mapstruct.NoticeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Optional;

import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-05-13
**/
@Slf4j
@Service
@RequiredArgsConstructor
public class NoticeServiceImpl implements NoticeService {

    private final NoticeRepository noticeRepository;
    private final NoticeMapper noticeMapper;

    @DubboReference
    private RemoteManageService remoteManageService;

    @Override
    public PageResult<NoticeDto> queryAll(NoticeQueryCriteria criteria, Pageable pageable){
        Page<Notice> page = noticeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(noticeMapper::toDto));
    }

    @Override
    public List<NoticeDto> queryAll(NoticeQueryCriteria criteria){
        return noticeMapper.toDto(noticeRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public NoticeDto findById(String id) {
        Notice notice = noticeRepository.findById(id).orElseGet(Notice::new);
        ValidationUtil.isNull(notice.getId(),"Notice","id",id);
        return noticeMapper.toDto(notice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Notice resources) {
        resources.setId(IdUtil.simpleUUID());
        if (resources.getStatus() == null) {
            resources.setStatus("Pending"); // 新增数据默认等待中状态 Pending
        }
        noticeRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Notice resources) {
        Notice notice = noticeRepository.findById(resources.getId()).orElseGet(Notice::new);
        ValidationUtil.isNull( notice.getId(),"Notice","id",resources.getId());
        notice.copy(resources);
        noticeRepository.save(notice);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            Optional<Notice> byId = noticeRepository.findById(id);
            if (byId.isPresent()) {
                Notice notice = byId.get();
                notice.setStatus("Deleted"); // 逻辑删除，设置状态为 Deleted
                noticeRepository.save(notice);
            }
            // noticeRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<NoticeDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (NoticeDto notice : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("标题", notice.getTitle());
            map.put("副标题", notice.getSubTitle());
            map.put("通知内容", notice.getContent());
            map.put("跳转链接", notice.getLink());
            map.put("通知时间", notice.getNotifyTime());
            map.put("通知状态", notice.getStatus());
            map.put("目标用户", notice.getSendTo());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public boolean publish(String id) {
        log.info("publish notice {}", id);
        Optional<Notice> byId = noticeRepository.findById(id);
        if (byId.isEmpty()) {
            log.error("notice not found, id: {}", id);
            return false;
        }
        Notice notice = byId.get();
        if (!byId.get().getStatus().equals("Pending")) {
            log.error("not publish notice status, id: {} status {}", id, byId.get().getStatus());
            return false;
        }
        notice.setStatus("Completed"); // 发布之后，消息状态变成 Completed

        NoticeDTO noticeDTO = new NoticeDTO();
        BeanUtils.copyProperties(byId.get(), noticeDTO);
        Response<Boolean> booleanResponse = remoteManageService.saveNotice(noticeDTO);
        if (!booleanResponse.isSuccess() || !booleanResponse.getData()) {
            log.error("publish notice failed, id: {}", id);
            return false;
        }
        noticeRepository.save(notice);
        return true;
    }
}