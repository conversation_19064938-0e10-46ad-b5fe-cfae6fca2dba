/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.notify.rest;

import com.stanly.admin.annotation.Log;
import com.stanly.drex.notify.domain.Notice;
import com.stanly.drex.notify.service.NoticeService;
import com.stanly.drex.notify.service.dto.NoticeQueryCriteria;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;
import com.stanly.drex.notify.service.dto.NoticeDto;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-05-13
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "通知消息配置管理")
@RequestMapping("/api/notice")
public class NoticeController {

    private final NoticeService noticeService;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('notice:list')")
    public void exportNotice(HttpServletResponse response, NoticeQueryCriteria criteria) throws IOException {
        noticeService.download(noticeService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询通知消息配置")
    @Operation(summary = "查询通知消息配置")
    @PreAuthorize("@el.check('notice:list')")
    public ResponseEntity<PageResult<NoticeDto>> queryNotice(NoticeQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(noticeService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增通知消息配置")
    @Operation(summary = "新增通知消息配置")
    @PreAuthorize("@el.check('notice:add')")
    public ResponseEntity<Object> createNotice(@Validated @RequestBody Notice resources){
        noticeService.create(resources);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改通知消息配置")
    @Operation(summary = "修改通知消息配置")
    @PreAuthorize("@el.check('notice:edit')")
    public ResponseEntity<Object> updateNotice(@Validated @RequestBody Notice resources){
        noticeService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除通知消息配置")
    @Operation(summary = "删除通知消息配置")
    @PreAuthorize("@el.check('notice:del')")
    public ResponseEntity<Object> deleteNotice(@RequestBody String[] ids) {
        noticeService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("发布通知消息")
    @Operation(summary = "发布通知消息")
    @PostMapping(value = "/publish")
    public ResponseEntity<Object> publishNotice(@RequestBody String id) {
        if (StringUtils.isBlank(id)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if (noticeService.publish(id)) {
            return new ResponseEntity<>("发布成功", HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }
}