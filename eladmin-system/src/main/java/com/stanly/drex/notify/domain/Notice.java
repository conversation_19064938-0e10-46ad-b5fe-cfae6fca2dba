/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.notify.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-13
**/
@Entity
@Data
@Table(name="notice")
public class Notice implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "消息id")
    private String id;

    @Column(name = "`title`",nullable = false)
    @NotBlank
    @Schema(name = "标题")
    private String title;

    @Column(name = "`sub_title`")
    @Schema(name = "副标题")
    private String subTitle;

    @Column(name = "`content`")
    @Schema(name = "通知内容")
    private String content;

    @Column(name = "`link`")
    @Schema(name = "跳转链接")
    private String link;

    @Column(name = "`notify_time`")
    @Schema(name = "通知时间")
    private Long notifyTime;

    @Column(name = "`status`")
    @Schema(name = "通知状态")
    private String status;

    @Column(name = "`send_to`")
    @Schema(name = "发送目标 1 所有用户 2 内测用户")
    private Integer sendTo;

    public void copy(Notice source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
