# 高校域名白名单管理模块

## 功能概述

高校域名白名单管理模块提供了完整的CRUD操作，用于管理高校及其对应的域名信息。该模块支持：

- 高校域名信息的增删改查
- 批量导入CSV文件
- 数据导出
- 权限控制
- 数据验证

## 模块结构

```
com.stanly.drex.core.university/
├── domain/
│   └── University.java              # 实体类
├── repository/
│   └── UniversityRepository.java    # 数据访问层
├── service/
│   ├── UniversityService.java       # 服务接口
│   ├── impl/
│   │   └── UniversityServiceImpl.java # 服务实现
│   ├── dto/
│   │   ├── UniversityDto.java       # 数据传输对象
│   │   └── UniversityQueryCriteria.java # 查询条件
│   └── mapstruct/
│       └── UniversityMapper.java    # 对象映射
└── rest/
    └── UniversityController.java    # REST控制器
```

## API接口

### 1. 分页查询
```
GET /api/university
```
支持的查询参数：
- `university`: 学校名称（模糊查询）
- `domain`: 域名（模糊查询）
- `page`: 页码（从0开始）
- `size`: 每页大小

### 2. 新增记录
```
POST /api/university
Content-Type: application/json

{
    "university": "北京大学",
    "domain": "pku.edu.cn"
}
```

### 3. 更新记录
```
PUT /api/university
Content-Type: application/json

{
    "id": 1,
    "university": "北京大学",
    "domain": "mail.pku.edu.cn"
}
```

### 4. 删除记录
```
DELETE /api/university
Content-Type: application/json

[1, 2, 3]
```

### 5. 批量导入
```
POST /api/university/batch-import
Content-Type: multipart/form-data

file: university.csv
```

CSV文件格式（无表头）：
```
北京大学,pku.edu.cn
清华大学,tsinghua.edu.cn
复旦大学,fudan.edu.cn
```

### 6. 数据导出
```
GET /api/university/download
```

## 权限配置

模块使用以下权限标识：
- `university:list` - 查看权限
- `university:add` - 新增权限
- `university:edit` - 编辑权限
- `university:del` - 删除权限

## 数据验证

### 学校名称
- 不能为空
- 在同一系统中必须唯一

### 域名
- 不能为空
- 必须符合域名格式规范
- 在同一系统中必须唯一

域名格式验证正则表达式：
```
^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$
```

## 数据库表结构

```sql
CREATE TABLE `university_whitelist` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
    `university` VARCHAR(255) NOT NULL COMMENT '学校名称',
    `domain` VARCHAR(255) NOT NULL COMMENT '域名',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_university` (`university`),
    UNIQUE KEY `uk_domain` (`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高校域名白名单表';
```

## 菜单配置

在系统菜单中，高校域名白名单位于：
```
D-Rex > 高校域名白名单
```

路由路径：`/drex/university`
组件路径：`drex/university/index`

## 使用示例

### 1. 创建新记录
```java
University university = new University();
university.setUniversity("北京大学");
university.setDomain("pku.edu.cn");
UniversityDto result = universityService.create(university);
```

### 2. 查询记录
```java
UniversityQueryCriteria criteria = new UniversityQueryCriteria();
criteria.setUniversity("北京");
List<UniversityDto> results = universityService.queryAll(criteria);
```

### 3. 批量导入
```java
MultipartFile csvFile = ...; // CSV文件
Map<String, Object> result = universityService.batchImport(csvFile);
```

## 错误处理

### 常见错误
1. **学校名称已存在**: 当尝试创建或更新时学校名称与现有记录冲突
2. **域名已存在**: 当尝试创建或更新时域名与现有记录冲突
3. **域名格式不正确**: 域名不符合标准格式
4. **文件格式错误**: 批量导入时文件不是CSV格式或格式不正确

### 批量导入结果
批量导入返回详细的结果信息：
```json
{
    "total": 100,
    "success": 95,
    "error": 5,
    "successList": ["第1行：北京大学 - pku.edu.cn", ...],
    "errorList": ["第10行：学校名称已存在 - 清华大学", ...]
}
```

## 测试

运行单元测试：
```bash
mvn test -Dtest=UniversityServiceTest
```

测试覆盖了以下场景：
- 基本CRUD操作
- 数据验证
- 重复数据检查
- 查询功能
- 异常处理

## 注意事项

1. 确保数据库已执行相关的建表和菜单配置SQL
2. 重新登录系统以获取最新的菜单权限
3. CSV导入文件必须使用UTF-8编码
4. 域名验证严格按照RFC标准执行
5. 删除操作不可恢复，请谨慎操作
