/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.university.rest;

import com.stanly.admin.annotation.Log;
import com.stanly.drex.core.university.domain.University;
import com.stanly.drex.core.university.service.UniversityService;
import com.stanly.drex.core.university.service.dto.UniversityQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-08-11
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "高校域名白名单管理")
@RequestMapping("/api/university")
public class UniversityController {

    private final UniversityService universityService;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('university:list')")
    public void exportUniversity(HttpServletResponse response, UniversityQueryCriteria criteria) throws IOException {
        universityService.download(universityService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询高校域名白名单")
    @Operation(summary = "查询高校域名白名单")
    @PreAuthorize("@el.check('university:list')")
    public ResponseEntity<Object> queryUniversity(UniversityQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(universityService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增高校域名白名单")
    @Operation(summary = "新增高校域名白名单")
    @PreAuthorize("@el.check('university:add')")
    public ResponseEntity<Object> createUniversity(@Validated @RequestBody University resources){
        return new ResponseEntity<>(universityService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改高校域名白名单")
    @Operation(summary = "修改高校域名白名单")
    @PreAuthorize("@el.check('university:edit')")
    public ResponseEntity<Object> updateUniversity(@Validated @RequestBody University resources){
        universityService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除高校域名白名单")
    @Operation(summary = "删除高校域名白名单")
    @PreAuthorize("@el.check('university:del')")
    public ResponseEntity<Object> deleteUniversity(@RequestBody Long[] ids) {
        universityService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/batch-import")
    @Log("批量导入高校域名白名单")
    @Operation(summary = "批量导入高校域名白名单")
    @PreAuthorize("@el.check('university:add')")
    public ResponseEntity<Object> batchImport(@RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = universityService.batchImport(file);
            return new ResponseEntity<>(result, HttpStatus.OK);
        } catch (IOException e) {
            return new ResponseEntity<>("文件处理失败: " + e.getMessage(), HttpStatus.BAD_REQUEST);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
}
