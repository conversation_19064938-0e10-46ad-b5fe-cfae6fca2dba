/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.drex.core.api.RemoteInformationService;
import com.kikitrade.framework.common.model.Response;
import com.stanly.admin.utils.*;
import com.stanly.drex.core.information.domain.Information;
import lombok.RequiredArgsConstructor;
import com.stanly.drex.core.information.repository.InformationRepository;
import com.stanly.drex.core.information.service.InformationService;
import com.stanly.drex.core.information.service.dto.InformationDto;
import com.stanly.drex.core.information.service.dto.InformationQueryCriteria;
import com.stanly.drex.core.information.service.mapstruct.InformationMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-05-06
**/
@Service
@Slf4j
@RequiredArgsConstructor
public class InformationServiceImpl implements InformationService {

    private final InformationRepository informationRepository;
    private final InformationMapper informationMapper;
    private final UploadAliyunOssUtil uploadAliyunOssUtil;
    private final RestTemplate restTemplate;

    @Value("${google.api.key:}")
    private String googleApiKey;

    @DubboReference
    private RemoteInformationService remoteInformationService;

    @Override
    public PageResult<InformationDto> queryAll(InformationQueryCriteria criteria, Pageable pageable){
        // 如果指定了position，使用position的自定义排序
        if (criteria != null && criteria.getPosition() != null && !criteria.getPosition().trim().isEmpty()) {
            // 检查是否只有position条件
            if (isOnlyPositionCriteria(criteria)) {
                Page<Information> page = informationRepository.findByPositionWithCustomSort(criteria.getPosition(), pageable);
                return PageUtil.toPage(page.map(informationMapper::toDto));
            }
        }

        // 如果没有查询条件，使用全局自定义排序
        if (isEmptyCriteria(criteria)) {
            return queryAllWithCustomSort(pageable);
        }

        // 有其他查询条件时，使用原有的查询方式
        Page<Information> page = informationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(informationMapper::toDto));
    }

    /**
     * 检查是否只有position条件
     */
    private boolean isOnlyPositionCriteria(InformationQueryCriteria criteria) {
        if (criteria == null || criteria.getPosition() == null) return false;
        return criteria.getId() == null &&
               criteria.getType() == null &&
               criteria.getName() == null &&
               criteria.getTitle() == null &&
               criteria.getCategory() == null &&
               criteria.getIsRecommend() == null;
    }

    /**
     * 检查查询条件是否为空
     */
    private boolean isEmptyCriteria(InformationQueryCriteria criteria) {
        if (criteria == null) return true;
        return criteria.getId() == null &&
               criteria.getType() == null &&
               criteria.getName() == null &&
               criteria.getTitle() == null &&
               criteria.getCategory() == null &&
               criteria.getIsRecommend() == null &&
               criteria.getPosition() == null;
    }

    @Override
    public List<InformationDto> queryAll(InformationQueryCriteria criteria){
        // 如果指定了position，使用position的自定义排序
        if (criteria != null && criteria.getPosition() != null && !criteria.getPosition().trim().isEmpty()) {
            // 检查是否只有position条件
            if (isOnlyPositionCriteria(criteria)) {
                List<Information> informationList = informationRepository.findByPositionWithCustomSort(criteria.getPosition());
                return informationMapper.toDto(informationList);
            }
        }

        // 如果没有查询条件，使用全局自定义排序
        if (isEmptyCriteria(criteria)) {
            return queryAllWithCustomSort();
        }

        // 有其他查询条件时，使用原有的查询方式
        return informationMapper.toDto(informationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public PageResult<InformationDto> queryAllWithCustomSort(Pageable pageable) {
        Page<Information> page = informationRepository.findAllWithCustomSort(pageable);
        return PageUtil.toPage(page.map(informationMapper::toDto));
    }

    @Override
    public List<InformationDto> queryAllWithCustomSort() {
        List<Information> informationList = informationRepository.findAllWithCustomSort();
        return informationMapper.toDto(informationList);
    }

    @Override
    @Transactional
    public InformationDto findById(String id) {
        Information information = informationRepository.findById(id).orElseGet(Information::new);
        ValidationUtil.isNull(information.getId(),"Information","id",id);
        return informationMapper.toDto(information);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Information create(Information resources) {
        // 使用UUID生成ID
        resources.setId(cn.hutool.core.util.IdUtil.randomUUID());
        resources.setStatus(0L); // 新建时默认未发布状态
        if (resources.getLink() != null) {
            resources.setVideoDuration(queryVideoDuration(resources.getType(), resources.getLink()));
        }
        // 设置创建时间
        if (resources.getCreated() == null) {
            resources.setCreated(System.currentTimeMillis());
        }
        return informationRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Information update(Information resources) {
        Information information = informationRepository.findById(resources.getId()).orElseGet(Information::new);
        ValidationUtil.isNull( information.getId(),"Information","id",resources.getId());
        if (!resources.getLink().equals(information.getLink())) {
            resources.setVideoDuration(queryVideoDuration(resources.getType(), resources.getLink()));
        }
        information.copy(resources);
        // 设置修改时间
        information.setModified(System.currentTimeMillis());
        return informationRepository.save(information);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            informationRepository.findById(id).ifPresent(information -> {
                //先删除image和logo，tag待定
                uploadAliyunOssUtil.deleteObject("drex/core/information",information.getImage());
                uploadAliyunOssUtil.deleteObject("drex/core/information",information.getLogo());
            });
            informationRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<InformationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (InformationDto information : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("类型 podcast、dapp", information.getType());
            map.put("name", information.getName());
            map.put("logo", information.getLogo());
            map.put("标题", information.getTitle());
            map.put("副标题", information.getSubTitle());
            map.put("摘要", information.getSummary());
            map.put("内容", information.getContent());
            map.put("图片链接", information.getImage());
            map.put("外部链接", information.getLink());
            map.put("分类 [GameFi, SocialFi]", information.getCategory());
            map.put("推荐置顶", information.getIsRecommend());
            map.put("排序值", information.getSort());
            map.put("创建时间", information.getCreated());
            map.put("修改时间", information.getModified());
            map.put("tag", information.getTag());
            map.put("发布时间", information.getDate());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean publish(String id) {
        InformationDto informationDto = findById(id);
        if (informationDto.getStatus() != 0) {
            log.error("publish illegal, current status not 0");
            return false;
        }
        com.drex.core.api.response.InformationDTO information = new com.drex.core.api.response.InformationDTO();
        BeanUtils.copyProperties(informationDto, information);
        information.setIsRecommend(Boolean.parseBoolean(informationDto.getIsRecommend()));
        information.setIsReward("Y".equals(informationDto.getIsReward()));
        if (StringUtils.isNotBlank(informationDto.getCategory())) {
            List<String> categories = JSON.parseArray(informationDto.getCategory(), String.class);
            information.setCategory(categories);
        }
        String link = informationDto.getLink();
        if ("XPost".equals(informationDto.getType())) {
            information.setLinkId(link.substring(link.lastIndexOf("/") + 1));
        } else if ("YouTube".equals(informationDto.getType())) {
            if (link.contains("shorts")) {
                //短视频的解析
                information.setLinkId(link.substring(link.lastIndexOf("/") + 1));
            } else {
                information.setLinkId(link.substring(link.lastIndexOf("=") + 1));
            }
        }

        log.info("begin publish information {}", information);

        Response<Boolean> booleanResponse = remoteInformationService.saveInformation(information);
        log.info("end publish booleanResponse {}", booleanResponse);
        if (booleanResponse.getData()) {
            informationDto.setStatus(1L); // 发布成功后更新状态为已发布 1

            Information resources = new Information();
            BeanUtils.copyProperties(informationDto, resources);
            update(resources);
        }
        return booleanResponse.getData();
    }

    @Override
    public Boolean withdraw(String id) {
        InformationDto informationDto = findById(id);
        if (informationDto == null) {
            return Boolean.FALSE;
        }
        if (informationDto.getStatus() != 1) {
            log.error("withdraw illegal, current status not 1");
            return false;
        }
        log.info("begin withdraw information {}", informationDto);

        Response<Boolean> booleanResponse = remoteInformationService.deleteInformation(informationDto.getId());
        log.info("end withdraw booleanResponse {}", booleanResponse);
        if (booleanResponse.getData()) {
            informationDto.setStatus(0L); // 撤回成功后更新状态为未发布 0

            Information resources = new Information();
            BeanUtils.copyProperties(informationDto, resources);
            update(resources);
        }
        return booleanResponse.getData();
    }

    public Boolean updateRemote(String id) {
        InformationDto informationDto = findById(id);
        com.drex.core.api.response.InformationDTO information = new com.drex.core.api.response.InformationDTO();
        BeanUtils.copyProperties(informationDto, information);
        information.setIsRecommend(Boolean.parseBoolean(informationDto.getIsRecommend()));
        information.setIsReward("Y".equals(informationDto.getIsReward()));
        if (StringUtils.isNotBlank(informationDto.getCategory())) {
            List<String> categories = JSON.parseArray(informationDto.getCategory(), String.class);
            information.setCategory(categories);
        }
        String link = informationDto.getLink();
        if ("XPost".equals(informationDto.getType())) {
            information.setLinkId(link.substring(link.lastIndexOf("/") + 1));
        } else if ("YouTube".equals(informationDto.getType())) {
            if (link.contains("shorts")) {
                //短视频的解析
                information.setLinkId(link.substring(link.lastIndexOf("/") + 1));
            } else {
                information.setLinkId(link.substring(link.lastIndexOf("=") + 1));
            }
        }
        log.info("begin update information {}", information);

        Response<Boolean> booleanResponse = remoteInformationService.updateInformation(information);
        log.info("end update booleanResponse {}", booleanResponse);
        return booleanResponse.getData();
    }

    @Override
    public Long queryVideoDuration(String type, String link) {
        if (!"YouTube".equals(type)) {
            log.warn("不支持的视频类型: {}", type);
            return null;
        }

        if (StringUtils.isBlank(link)) {
            log.warn("link 不能为空");
            return null;
        }
        String linkId;
        if (link.contains("shorts")) {
            //短视频的解析
            linkId = link.substring(link.lastIndexOf("/") + 1);
        } else {
            linkId = link.substring(link.lastIndexOf("=") + 1);
        }
        if (StringUtils.isBlank(linkId)) {
            log.warn("linkId 不能为空");
            return null;
        }

        if (StringUtils.isBlank(googleApiKey)) {
            log.error("Google API Key 未配置");
            return null;
        }

        try {
            // 构造 YouTube API 请求 URL
            String url = String.format("https://youtube.googleapis.com/youtube/v3/videos?part=contentDetails&id=%s&key=%s",
                                     linkId, googleApiKey);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Accept", "application/json");
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 HTTP 请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                // 解析响应 JSON
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                JSONArray items = jsonResponse.getJSONArray("items");

                if (items != null && !items.isEmpty()) {
                    JSONObject firstItem = items.getJSONObject(0);
                    JSONObject contentDetails = firstItem.getJSONObject("contentDetails");

                    if (contentDetails != null) {
                        String duration = contentDetails.getString("duration");
                        if (StringUtils.isNotBlank(duration)) {
                            // 解析 ISO 8601 时长格式并转换为秒数
                            Long durationInSeconds = parseISO8601Duration(duration);
                            log.info("YouTube 视频 {} 时长: {} 秒", linkId, durationInSeconds);
                            return durationInSeconds;
                        }
                    }
                }
            }

            log.warn("未能获取到视频时长信息，linkId: {}", linkId);
            return null;

        } catch (Exception e) {
            log.error("查询 YouTube 视频时长失败，linkId: {}", linkId, e);
            return null;
        }
    }

    /**
     * 解析 ISO 8601 时长格式（如 PT23M24S）并转换为总秒数
     *
     * @param duration ISO 8601 格式的时长字符串
     * @return 总秒数
     */
    private Long parseISO8601Duration(String duration) {
        if (StringUtils.isBlank(duration)) {
            return null;
        }

        // 正则表达式匹配 PT(\d+H)?(\d+M)?(\d+S)?
        Pattern pattern = Pattern.compile("PT(?:(\\d+)H)?(?:(\\d+)M)?(?:(\\d+)S)?");
        Matcher matcher = pattern.matcher(duration);

        if (matcher.matches()) {
            long hours = 0L;
            long minutes = 0L;
            long seconds = 0L;

            // 提取小时
            String hoursStr = matcher.group(1);
            if (hoursStr != null) {
                hours = Long.parseLong(hoursStr);
            }

            // 提取分钟
            String minutesStr = matcher.group(2);
            if (minutesStr != null) {
                minutes = Long.parseLong(minutesStr);
            }

            // 提取秒
            String secondsStr = matcher.group(3);
            if (secondsStr != null) {
                seconds = Long.parseLong(secondsStr);
            }

            // 转换为总秒数
            Long totalSeconds = hours * 3600 + minutes * 60 + seconds;
            log.debug("解析时长: {} -> {}小时{}分钟{}秒 = {}秒", duration, hours, minutes, seconds, totalSeconds);
            return totalSeconds;
        }

        log.warn("无法解析时长格式: {}", duration);
        return null;
    }
}