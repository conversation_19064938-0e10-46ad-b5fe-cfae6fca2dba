/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.university.service.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @website https://eladmin.vip
* @description 高校域名白名单DTO
* <AUTHOR>
* @date 2025-08-11
**/
@Data
public class UniversityDto implements Serializable {

    /** 主键 */
    private Long id;

    /** 学校名称 */
    private String university;

    /** 域名 */
    private String domain;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
}
