/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-06
**/
@Data
public class InformationDto implements Serializable {

    /** 主键 */
    private String id;

    /** 类型 podcast、dapp */
    private String type;

    /** name */
    private String name;

    /** logo */
    private String logo;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 摘要 */
    private String summary;

    /** 内容 */
    private String content;

    /** 图片链接 */
    private String image;

    /** 外部链接 */
    private String link;

    /** 分类 [GameFi, SocialFi] */
    private String category;

    /** 推荐置顶 */
    private String isRecommend;

    /** 状态 0 未发布 1 已发布 */
    private Long status;

    /** 排序值 */
    private Integer sort;

    /** 来源icon */
    private String tag;

    /** 发布时间 */
    private Long date;

    /** 展示位置 */
    private String position;

    /** 活动举办方 */
    private String organizer;

    /** 活动地点 */
    private String location;

    /** 活动开始时间 */
    private Long activityStartTime;

    /** 活动结束时间 */
    private Long activityEndTime;

    /** 是否有奖励 */
    private String isReward;

    /** 奖励数量 */
    private String rewardAmount;

    private Long videoDuration;

    private String xMention;

    private Integer xLength;

    /** 创建时间 */
    private Long created;

    /** 修改时间 */
    private Long modified;
}