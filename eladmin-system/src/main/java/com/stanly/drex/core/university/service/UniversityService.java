/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.university.service;

import com.stanly.admin.utils.PageResult;
import com.stanly.drex.core.university.domain.University;
import com.stanly.drex.core.university.service.dto.UniversityDto;
import com.stanly.drex.core.university.service.dto.UniversityQueryCriteria;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.List;

/**
* @website https://eladmin.vip
* @description 高校域名白名单服务接口
* <AUTHOR>
* @date 2025-08-11
**/
public interface UniversityService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<UniversityDto> queryAll(UniversityQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<UniversityDto>
    */
    List<UniversityDto> queryAll(UniversityQueryCriteria criteria);

    /**
     * 根据ID查询
     * @param id ID
     * @return UniversityDto
     */
    UniversityDto findById(Long id);

    /**
    * 创建
    * @param resources /
    */
    UniversityDto create(University resources);

    /**
    * 编辑
    * @param resources /
    */
    void update(University resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(Long[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<UniversityDto> all, HttpServletResponse response) throws IOException;

    /**
     * 批量导入
     * @param file CSV文件
     * @return 导入结果
     * @throws IOException /
     */
    Map<String, Object> batchImport(MultipartFile file) throws IOException;
}
