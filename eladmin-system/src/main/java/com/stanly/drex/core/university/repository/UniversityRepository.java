/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.university.repository;

import com.stanly.drex.core.university.domain.University;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-08-11
**/
public interface UniversityRepository extends JpaRepository<University, Long>, JpaSpecificationExecutor<University> {

    /**
     * 根据学校名称查询
     * @param university 学校名称
     * @return University
     */
    Optional<University> findByUniversity(String university);

    /**
     * 根据域名查询
     * @param domain 域名
     * @return University
     */
    Optional<University> findByDomain(String domain);

    /**
     * 检查学校名称是否存在（排除指定ID）
     * @param university 学校名称
     * @param id 排除的ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(u) > 0 FROM University u WHERE u.university = :university AND u.id != :id")
    boolean existsByUniversityAndIdNot(@Param("university") String university, @Param("id") Long id);

    /**
     * 检查域名是否存在（排除指定ID）
     * @param domain 域名
     * @param id 排除的ID
     * @return 是否存在
     */
    @Query("SELECT COUNT(u) > 0 FROM University u WHERE u.domain = :domain AND u.id != :id")
    boolean existsByDomainAndIdNot(@Param("domain") String domain, @Param("id") Long id);
}
