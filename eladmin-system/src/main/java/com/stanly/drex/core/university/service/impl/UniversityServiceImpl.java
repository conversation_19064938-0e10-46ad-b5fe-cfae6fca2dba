/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.university.service.impl;

import com.stanly.admin.utils.*;
import com.stanly.drex.core.information.service.dto.InformationDto;
import com.stanly.drex.core.university.domain.University;
import lombok.RequiredArgsConstructor;
import com.stanly.drex.core.university.repository.UniversityRepository;
import com.stanly.drex.core.university.service.UniversityService;
import com.stanly.drex.core.university.service.dto.UniversityDto;
import com.stanly.drex.core.university.service.dto.UniversityQueryCriteria;
import com.stanly.drex.core.university.service.mapstruct.UniversityMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.io.IOException;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-08-11
**/
@Service
@RequiredArgsConstructor
public class UniversityServiceImpl implements UniversityService {

    private final UniversityRepository universityRepository;
    private final UniversityMapper universityMapper;

    @Override
    public PageResult<UniversityDto> queryAll(UniversityQueryCriteria criteria, Pageable pageable){
        Page<University> page = universityRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(universityMapper::toDto));
    }

    @Override
    public List<UniversityDto> queryAll(UniversityQueryCriteria criteria){
        return universityMapper.toDto(universityRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public UniversityDto findById(Long id) {
        University university = universityRepository.findById(id).orElse(null);
        ValidationUtil.isNull(university,"University","id",id);
        return universityMapper.toDto(university);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UniversityDto create(University resources) {
        // 检查学校名称是否已存在
        if (universityRepository.findByUniversity(resources.getUniversity()).isPresent()) {
            throw new RuntimeException("学校名称已存在");
        }
        // 检查域名是否已存在
        if (universityRepository.findByDomain(resources.getDomain()).isPresent()) {
            throw new RuntimeException("域名已存在");
        }
        return universityMapper.toDto(universityRepository.save(resources));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(University resources) {
        University university = universityRepository.findById(resources.getId()).orElse(null);
        ValidationUtil.isNull(university,"University","id",resources.getId());
        
        // 检查学校名称是否已存在（排除当前记录）
        if (universityRepository.existsByUniversityAndIdNot(resources.getUniversity(), resources.getId())) {
            throw new RuntimeException("学校名称已存在");
        }
        // 检查域名是否已存在（排除当前记录）
        if (universityRepository.existsByDomainAndIdNot(resources.getDomain(), resources.getId())) {
            throw new RuntimeException("域名已存在");
        }
        
        university.copy(resources);
        universityRepository.save(university);
    }

    @Override
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            universityRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<UniversityDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (UniversityDto university : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("学校名称", university.getUniversity());
            map.put("域名", university.getDomain());
            map.put("创建时间", university.getCreateTime());
            map.put("更新时间", university.getUpdateTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImport(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
            throw new RuntimeException("只支持CSV格式文件");
        }

        List<String> successList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        int totalCount = 0;

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                totalCount++;
                line = line.trim();
                if (line.isEmpty()) {
                    continue;
                }

                String[] parts = line.split(",");
                if (parts.length != 2) {
                    errorList.add("第" + totalCount + "行：格式错误，应为：学校名称,域名");
                    continue;
                }

                String university = parts[0].trim();
                String domain = parts[1].trim();

                if (university.isEmpty() || domain.isEmpty()) {
                    errorList.add("第" + totalCount + "行：学校名称和域名不能为空");
                    continue;
                }

                // 验证域名格式
                if (!domain.matches("^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$")) {
                    errorList.add("第" + totalCount + "行：域名格式不正确 - " + domain);
                    continue;
                }

                // 检查是否已存在
                if (universityRepository.findByUniversity(university).isPresent()) {
                    errorList.add("第" + totalCount + "行：学校名称已存在 - " + university);
                    continue;
                }

                if (universityRepository.findByDomain(domain).isPresent()) {
                    errorList.add("第" + totalCount + "行：域名已存在 - " + domain);
                    continue;
                }

                try {
                    University newUniversity = new University();
                    newUniversity.setUniversity(university);
                    newUniversity.setDomain(domain);
                    universityRepository.save(newUniversity);
                    successList.add("第" + totalCount + "行：" + university + " - " + domain);
                } catch (Exception e) {
                    errorList.add("第" + totalCount + "行：保存失败 - " + e.getMessage());
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("total", totalCount);
        result.put("success", successList.size());
        result.put("error", errorList.size());
        result.put("successList", successList);
        result.put("errorList", errorList);

        return result;
    }
}
