/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.service;

import com.stanly.drex.core.information.domain.Information;
import com.stanly.drex.core.information.service.dto.InformationDto;
import com.stanly.drex.core.information.service.dto.InformationQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务接口
* <AUTHOR>
* @date 2025-05-06
**/
public interface InformationService {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<InformationDto> queryAll(InformationQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<InformationDto>
    */
    List<InformationDto> queryAll(InformationQueryCriteria criteria);

    /**
     * 查询数据分页（自定义排序）
     * @param pageable 分页参数
     * @return PageResult<InformationDto>
     */
    PageResult<InformationDto> queryAllWithCustomSort(Pageable pageable);

    /**
     * 查询所有数据不分页（自定义排序）
     * @return List<InformationDto>
     */
    List<InformationDto> queryAllWithCustomSort();

    /**
     * 根据ID查询
     * @param id ID
     * @return InformationDto
     */
    InformationDto findById(String id);

    /**
    * 创建
    * @param resources /
    */
    Information create(Information resources);

    /**
    * 编辑
    * @param resources /
    */
    Information update(Information resources);

    /**
    * 多选删除
    * @param ids /
    */
    void deleteAll(String[] ids);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<InformationDto> all, HttpServletResponse response) throws IOException;

    /**
     * 发布项目信息
     * @param id
     */
    Boolean publish(String id);

    /**
     * 撤回项目信息
     * @param id
     */
    Boolean withdraw(String id);

    /**
     * 更新远程项目信息
     * @param id
     */
    Boolean updateRemote(String id);

    /**
     * 查询视频时长
     * @param type 视频类型（如 YouTube）
     * @param linkId 视频ID
     * @return 视频时长（秒）
     */
    Long queryVideoDuration(String type, String linkId);

}