/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-06
**/
@Entity
@Data
@Table(name="information")
public class Information implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "主键")
    private String id;

    @Column(name = "`position`", nullable = false)
    @NotBlank
    @Schema(name = "展示位置")
    private String position;

    @Column(name = "`type`")
    @Schema(name = "类型 Article、DApp、News等")
    private String type;

    @Column(name = "`name`")
    @Schema(name = "名称")
    private String name;

    @Column(name = "`logo`")
    @Schema(name = "logo链接")
    private String logo;

    @Column(name = "`title`",nullable = false)
    @NotBlank
    @Schema(name = "标题")
    private String title;

    @Column(name = "`sub_title`")
    @Schema(name = "副标题")
    private String subTitle;

    @Column(name = "`summary`")
    @Schema(name = "摘要")
    private String summary;

    @Column(name = "`content`")
    @Schema(name = "内容")
    private String content;

    @Column(name = "`image`")
    @Schema(name = "图片链接")
    private String image;

    @Column(name = "`link`")
    @Schema(name = "外部链接")
    private String link;

    @Column(name = "`category`")
    @Schema(name = "分类 [GameFi, SocialFi]")
    private String category;

    @Column(name = "`is_recommend`")
    @Schema(name = "推荐置顶")
    private String isRecommend;

    @Column(name = "`status`")
    @Schema(name = "发布状态 0 未发布 1 已发布")
    private Long status;

    @Column(name = "`sort`")
    @Schema(name = "排序值")
    private Integer sort;

    @Column(name = "`tag`")
    @Schema(name = "来源icon")
    private String tag;

    @Column(name = "`organizer`")
    @Schema(name = "活动举办方")
    private String organizer;

    @Column(name = "`location`")
    @Schema(name = "活动地点")
    private String location;

    @Column(name = "`date`")
    @Schema(name = "发布时间")
    private Long date;

    @Column(name = "`activity_start_time`")
    @Schema(name = "活动开始时间")
    private Long activityStartTime;

    @Column(name = "`activity_end_time`")
    @Schema(name = "活动结束时间")
    private Long activityEndTime;

    @Column(name = "`created`")
    @Schema(name = "创建时间")
    private Long created;

    @Column(name = "`modified`")
    @Schema(name = "修改时间")
    private Long modified;

    @Column(name = "`is_reward`")
    @Schema(name = "是否有奖励")
    private String isReward;

    @Column(name = "`reward_amount`")
    @Schema(name = "奖励数量")
    private String rewardAmount;

    @Column(name = "`video_duration`")
    @Schema(name = "视频时长")
    private Long videoDuration;

    @Column(name = "`x_mention`")
    @Schema(name = "x_mention")
    private String xMention;

    @Column(name = "`x_length`")
    @Schema(name = "x_length")
    private Integer xLength;

    public void copy(Information source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
