package com.stanly.ai.rag;

import com.alibaba.cloud.ai.dashscope.agent.DashScopeAgent;
import com.alibaba.cloud.ai.dashscope.agent.DashScopeAgentOptions;
import com.alibaba.cloud.ai.dashscope.api.DashScopeAgentApi;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@RestController
@Tag(name = "ai接口")
@RequestMapping("/ai/")
public class AiController {

    private static final Logger logger = LoggerFactory.getLogger(AiController.class);

	private final DashScopeAgentApi dashscopeAgentApi;

	private Map<String, Agent> agents = new HashMap<>();

	public AiController(DashScopeAgentApi dashscopeAgentApi) {
		this.dashscopeAgentApi = dashscopeAgentApi;
		ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
		scheduler.schedule(new Runnable() {
			@Override
			public void run(){
				for (Map.Entry<String, Agent> entry : agents.entrySet()) {
					logger.info("agent: {}", entry.getKey());
				    if (entry.getValue().getExpireTime() < System.currentTimeMillis()) {
				        agents.remove(entry.getKey());
				        logger.info("remove agent: {}", entry.getKey());
				    }
				}
			}
		}
		, 1, TimeUnit.MINUTES);
	}

	@GetMapping("rag/stream")
	public Flux<String> stream(@RequestParam(value = "message") String message, @RequestParam(value = "appId") String appId, @RequestParam(value = "sessionId") String sessionId) {
		Prompt prompt = new Prompt(message, DashScopeAgentOptions.builder().withAppId(appId).build());
		logger.info(prompt.getContents());
		return getAgent(sessionId).stream(prompt)
			.map(response -> {
				if (response == null || response.getResult() == null) {
					logger.error("chat response is null");
					return "chat response is null";
				}

				AssistantMessage app_output = response.getResult().getOutput();
				String content = app_output.getText();

				DashScopeAgentApi.DashScopeAgentResponse.DashScopeAgentResponseOutput output = (DashScopeAgentApi.DashScopeAgentResponse.DashScopeAgentResponseOutput) app_output.getMetadata().get("output");
				List<DashScopeAgentApi.DashScopeAgentResponse.DashScopeAgentResponseOutput.DashScopeAgentResponseOutputDocReference> docReferences = output.docReferences();
				List<DashScopeAgentApi.DashScopeAgentResponse.DashScopeAgentResponseOutput.DashScopeAgentResponseOutputThoughts> thoughts = output.thoughts();

				return content;
			})
			.onErrorResume(e -> {
				// 处理错误，包括客户端断开连接的情况
				logger.warn("Error during streaming response: {}", e.getMessage());
				return Flux.error(e); // 返回空流，优雅地结束
			});
	}

	public DashScopeAgent getAgent(String seasonId) {
		Agent agent = agents.get(seasonId);
		if (agent == null) {
			agent = new Agent();
			agent.setExpireTime(System.currentTimeMillis() + 60 * 60 * 24);
			agent.setAgent(new DashScopeAgent(dashscopeAgentApi,
					DashScopeAgentOptions.builder()
					.withSessionId(seasonId)
					.withIncrementalOutput(true)
					.withHasThoughts(true)
					.build()));
			agents.put(seasonId, agent);
		}else{
			agent.setExpireTime(System.currentTimeMillis() + 60 * 60 * 24);
		}
		return agents.get(seasonId).getAgent();
	}

	public class  Agent {

		private long expireTime;
		private DashScopeAgent agent;

		public long getExpireTime() {
			return expireTime;
		}
		public void setExpireTime(long expireTime) {
			this.expireTime = expireTime;
		}
		public DashScopeAgent getAgent() {
			return agent;
		}
		public void setAgent(DashScopeAgent agent) {
			this.agent = agent;
		}
	}

}
