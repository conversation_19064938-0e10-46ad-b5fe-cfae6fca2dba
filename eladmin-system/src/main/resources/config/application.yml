server:
  port: 9090
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  data:
    redis:
      repositories:
        enabled: false
  cloud:
    compatibility-verifier:
      enabled: false
  ai:
    dashscope:
      api-key: sk-62705c5e8d9b44d2a75ebc39d7b6b471
  cache:
    type: redis
    cache-names: data
    redis:
      time-to-live: 600s
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
#  pid:
#    file: /自行指定位置/eladmin.pid

  #配置 Jpa
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

springdoc:
  swagger-ui:
    path: /doc.html
  api-docs:
    path: /api-docs

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/秒
code:
  expiration: 300

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

kiki:
  ons:
    address: http://*************:9876
    group-id: GID_CUSTOMER_MANAGE_DEV
    env: _DEV
    tag: blue
    enable-traffic: true

file:
  mac:
    path: ~/file/
    avatar: ~/avatar/
  linux:
    path: /home/<USER>/file/
    avatar: /home/<USER>/avatar/
  windows:
    path: C:\eladmin\file\
    avatar: C:\eladmin\avatar\
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5

logging:
  pattern:
    console: "[%p][%t][%d{yyyy-MM-dd HH:mm:ss.SSS}][%c][%L][%X{traceId}][%X{spanId}]%m%n"


