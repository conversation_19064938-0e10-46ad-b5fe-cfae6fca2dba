<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>manage-java</artifactId>
        <groupId>com.stanly</groupId>
        <version>2.7</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>eladmin-tools</artifactId>
    <name>工具模块</name>

    <properties>
        <mail.version>1.4.7</mail.version>
        <qiniu.version>7.9.3</qiniu.version>
        <alipay.version>4.22.57.ALL</alipay.version>
    </properties>

    <dependencies>
        <!-- 同时需要common模块和logging模块只需要引入logging模块即可 -->
        <dependency>
            <groupId>com.stanly</groupId>
            <artifactId>eladmin-logging</artifactId>
            <version>2.7</version>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>3.0.0</version>
        </dependency>

<!--        &lt;!&ndash;邮件依赖&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>javax.mail</groupId>-->
<!--            <artifactId>mail</artifactId>-->
<!--            <version>${mail.version}</version>-->
<!--        </dependency>-->

        <!--七牛云存储-->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>${qiniu.version}</version>
        </dependency>

        <!--支付宝依赖-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>${alipay.version}</version>
        </dependency>

        <!--aws S3存储依赖-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.12.310</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>

        <!-- csv文件解析依赖 -->
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.4</version>
        </dependency>
    </dependencies>
</project>
