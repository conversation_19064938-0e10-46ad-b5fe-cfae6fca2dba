package com.stanly.admin.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.TemporalField;
import java.time.temporal.WeekFields;
import java.util.Calendar;
import java.util.Date;

/**
 * @ClassName TimeUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2018/7/21 19:44
 * @Version 1.0
 **/
public class TimeUtil {

    public static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDDHHMM = "yyyy-MM-dd HH:mm";
    public static final String YYYYMMDD_000000 = "yyyy-MM-dd 00:00:00";
    public static final String YYYYMMDD_235959 = "yyyyMMdd235959";
    public static final String YYYYMMDDHH_5959 = "yyyyMMddHH5959";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String YYYYMM = "yyyy-MM";
    public static final String YYYYMMDDHH_0000 = "yyyyMMddHH0000";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String YYYYMMDDHH = "yyyyMMddHH";
    public static final String YYYYMMDDHHmmss = "yyyyMMddHHmmss";
    public static final String DD = "dd";

    public static final String YYYY_MM_DD_235959 = "yyyy-MM-dd 23:59:59";

    public static Date parseUnitTime(long unitTime) {
        return new Date(unitTime);
    }

    public static String getCurrentTime(String pattern) {
        return getCurrentTimeByZone(pattern, null);
    }

    public static String getCurrentUtcTime(String pattern) {
        return getCurrentTimeByZone(pattern, "+0");
    }

    public static String getCurrentTimeByZone(String pattern, String zoneId) {
        return ZonedDateTime.now(getZoneId(zoneId)).format(DateTimeFormatter.ofPattern(pattern));
    }

    public static Date parseDateTime(String date) {
        return parseDateTime(date, YYYYMMDDHHMMSS, null);
    }

    public static Date parseDateTime(String date, String pattern) {
        return parseDateTime(date, pattern, null);
    }

    public static Date parseDateTime(String date, String pattern, String zoneId) {
        try{
            return Date.from(LocalDateTime.parse(date, DateTimeFormatter.ofPattern(pattern)).atZone(getZoneId(zoneId)).toInstant());
        }catch (DateTimeParseException ex){
            return parseDate(date, pattern, zoneId);
        }
    }

    private static Date parseDate(String date, String pattern, String zoneId) {
        return Date.from(LocalDate.parse(date, DateTimeFormatter.ofPattern(pattern)).atStartOfDay(getZoneId(zoneId)).toInstant());
    }

    public static String format(Date date) {
        return format(date, YYYYMMDDHHMMSS);
    }

    public static String format(Date date, String pattern) {
        return format(date, pattern, null);
    }

    public static String format(Date date, String pattern, String zoneId) {
        LocalDateTime localDate = date.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String getUtcTime(Date date) {
        return getUtcTime(date, YYYYMMDDHHMMSS);
    }

    public static String getUtcTime(Date date, String pattern) {
        LocalDateTime localDate = date.toInstant().atZone(ZoneId.of("+0")).toLocalDateTime();
        return localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static Date addMinute(Date current, int minute) {
        return addMinute(current, minute, null);
    }

    public static Date addMinute(Date current, int minute, String zoneId) {
        LocalDateTime localDate = current.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return Date.from(localDate.plusMinutes(minute).atZone(getZoneId(zoneId)).toInstant());
    }

    public static Date addHour(Date current, int hours) {
        return addHour(current, hours, null);
    }

    public static Date addHour(Date current, int hours, String zoneId) {
        LocalDateTime localDate = current.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return Date.from(localDate.plusHours(hours).atZone(getZoneId(zoneId)).toInstant());
    }

    public static Date addDay(Date current, int days) {
       return addDay(current, days, null);
    }

    public static Date addDay(Date current, int days, String zoneId) {
        LocalDateTime localDate = current.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return Date.from(localDate.plusDays(days).atZone(getZoneId(zoneId)).toInstant());
    }

    public static Date addWeek(Date current, int weeks) {
        return addWeek(current, weeks, null);
    }

    public static Date addWeek(Date current, int weeks, String zoneId) {
        LocalDateTime localDate = current.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return Date.from(localDate.plusWeeks(weeks).atZone(getZoneId(zoneId)).toInstant());
    }

    public static Date addMonth(Date current, int months) {
        return addMonth(current, months, null);
    }

    public static Date addMonth(Date current, int months, String zoneId) {
        LocalDateTime localDate = current.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        return Date.from(localDate.plusMonths(months).atZone(getZoneId(zoneId)).toInstant());
    }

    /**
     * date的第一秒
     * @param date
     * @param timeZone
     * @return
     */
    public static Date getStartOfDay(Date date, String timeZone) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.of(timeZone)).toLocalDate();
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.of(timeZone));
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 获取本周的第一天
     *
     * @return String
     * **/
    public static String getWeekStart(Date date, String pattern, String zoneId){
        LocalDateTime localDate = date.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        LocalDateTime with = localDate.with(DayOfWeek.MONDAY);
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本周的第一天
     * zoneOffset -11 ~ +0 ~ +12 UTC +0 北京时间 +8 美国时间 -8
     * @return String
     * **/
    public static String getWeekEnd(Date date, String pattern, String zoneId){
        LocalDateTime localDate = date.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        LocalDateTime with = localDate.with(DayOfWeek.SUNDAY);
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本月的第一天
     * zoneOffset -11 ~ +0 ~ +12 UTC +0 北京时间 +8 美国时间 -8
     * @return String
     * **/
    public static String getMonthStart(Date date, String pattern, String zoneId){
        LocalDateTime localDate = date.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        LocalDateTime with = localDate.with(TemporalAdjusters.firstDayOfMonth());
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取本月的最后一天
     * @return String
     * **/
    public static String getMonthEnd(Date date, String pattern, String zoneId){
        LocalDateTime localDate = date.toInstant().atZone(getZoneId(zoneId)).toLocalDateTime();
        LocalDateTime with = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return with.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    public static Date parseWithUtc(String date, String formatter) {
        return new Date(LocalDateTime.parse(date, DateTimeFormatter.ofPattern(formatter)).atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
    }

    private static ZoneId getZoneId(String zoneId){
        return StringUtils.isNotBlank(zoneId) ? ZoneId.of(zoneId) : ZoneId.systemDefault();
    }

    public static void main(String[] args) {
        String utcTime = getUtcTime(new Date());
        System.out.println(utcTime);
        Date date = parseDateTime(utcTime);
        System.out.println(date);
    }
}
