package com.stanly.admin.utils;

import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/10/18 16:38
 */
@Component
public class IdUtils {

    @Resource
    RedisUtils redisUtils;

    private static final String ID_KEY = "task_config_id";

    /**
     * 生成递增的 taskId
     *
     * @return 递增的 taskId
     */
    public String generateTaskId() {
        // 使用 Redis 的 incr 操作获取递增的序列号
        long sequence = redisUtils.incr(ID_KEY);

        return String.valueOf(sequence);
    }
}
