package com.stanly.admin.utils;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * CSV文件工具类
 */
public class CsvUtil {
    
    private static final Logger log = LoggerFactory.getLogger(CsvUtil.class);
    
    /**
     * 导出CSV文件
     * 
     * @param list 数据列表，每个Map代表一行数据
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    public static void downloadCsv(List<Map<String, Object>> list, HttpServletResponse response) throws IOException {
        downloadCsv(list, response, true);
    }
    
    /**
     * 导出CSV文件
     * 
     * @param list 数据列表，每个Map代表一行数据
     * @param response HTTP响应对象
     * @param includeHeader 是否包含表头
     * @throws IOException IO异常
     */
    public static void downloadCsv(List<Map<String, Object>> list, HttpServletResponse response, boolean includeHeader) throws IOException {
        // 设置响应头
        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=data.csv");
        
        // 获取输出流
        ServletOutputStream out = response.getOutputStream();
        
        // CSV内容构建
        StringBuilder csvContent = new StringBuilder();
        
        // 如果有数据
        if (!list.isEmpty()) {
            Map<String, Object> firstRow = list.get(0);
            
            // 添加表头（如果需要）
            if (includeHeader) {
                boolean isFirstColumn = true;
                for (String key : firstRow.keySet()) {
                    if (!isFirstColumn) {
                        csvContent.append(",");
                    }
                    csvContent.append(escapeSpecialCharacters(key));
                    isFirstColumn = false;
                }
                csvContent.append("\n");
            }
            
            // 添加数据行
            for (Map<String, Object> row : list) {
                boolean isFirstColumn = true;
                for (Object value : row.values()) {
                    if (!isFirstColumn) {
                        csvContent.append(",");
                    }
                    csvContent.append(escapeSpecialCharacters(value != null ? value.toString() : ""));
                    isFirstColumn = false;
                }
                csvContent.append("\n");
            }
        }
        
        // 写入输出流
        out.write(csvContent.toString().getBytes("UTF-8"));
        out.flush();
        out.close();
    }
    
    /**
     * 转义CSV中的特殊字符
     * 
     * @param value 需要转义的字符串
     * @return 转义后的字符串
     */
    private static String escapeSpecialCharacters(String value) {
        if (value == null) {
            return "";
        }
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围
        boolean needQuotes = value.contains(",") || value.contains("\"") || value.contains("\n");
        
        if (needQuotes) {
            // 将双引号替换为两个双引号
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        }
        
        return value;
    }
}
