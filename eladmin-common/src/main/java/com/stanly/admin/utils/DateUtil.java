/*
 * Copyright 2019-2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.stanly.admin.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * @author: lia<PERSON><PERSON><PERSON>
 * @date: 2020/6/11 16:28
 * @apiNote: JDK 8  新日期类 格式化与字符串转换 工具类
 */
public class DateUtil {

    public static final DateTimeFormatter DFY_MD_HMS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DFY_MD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * LocalDateTime 转时间戳
     *
     * @param localDateTime /
     * @return /
     */
    public static Long getTimeStamp(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 时间戳转LocalDateTime
     *
     * @param timeStamp /
     * @return /
     */
    public static LocalDateTime fromTimeStamp(Long timeStamp) {
        return LocalDateTime.ofEpochSecond(timeStamp, 0, OffsetDateTime.now().getOffset());
    }

    /**
     * LocalDateTime 转 Date
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param localDateTime /
     * @return /
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDate 转 Date
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param localDate /
     * @return /
     */
    public static Date toDate(LocalDate localDate) {
        return toDate(localDate.atTime(LocalTime.now(ZoneId.systemDefault())));
    }


    /**
     * Date转 LocalDateTime
     * Jdk8 后 不推荐使用 {@link Date} Date
     *
     * @param date /
     * @return /
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 日期 格式化
     *
     * @param localDateTime /
     * @param patten /
     * @return /
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime, String patten) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(patten);
        return df.format(localDateTime);
    }

    /**
     * 日期 格式化
     *
     * @param localDateTime /
     * @param df /
     * @return /
     */
    public static String localDateTimeFormat(LocalDateTime localDateTime, DateTimeFormatter df) {
        return df.format(localDateTime);
    }

    /**
     * 日期格式化 yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime /
     * @return /
     */
    public static String localDateTimeFormatyMdHms(LocalDateTime localDateTime) {
        return DFY_MD_HMS.format(localDateTime);
    }

    /**
     * 日期格式化 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public String localDateTimeFormatyMd(LocalDateTime localDateTime) {
        return DFY_MD.format(localDateTime);
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormat(String localDateTime, String pattern) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.from(dateTimeFormatter.parse(localDateTime));
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormat(String localDateTime, DateTimeFormatter dateTimeFormatter) {
        return LocalDateTime.from(dateTimeFormatter.parse(localDateTime));
    }

    /**
     * 字符串转 LocalDateTime ，字符串格式 yyyy-MM-dd HH:mm:ss
     *
     * @param localDateTime /
     * @return /
     */
    public static LocalDateTime parseLocalDateTimeFormatyMdHms(String localDateTime) {
        return LocalDateTime.from(DFY_MD_HMS.parse(localDateTime));
    }

    public static Date toMirrorLosAngelesDate(Date date) {
        ZoneId losAngelesZone = ZoneId.of("America/Los_Angeles");
        LocalDateTime systemDate = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        long millis = Duration.between(systemDate.atZone(ZoneId.systemDefault()), systemDate.atZone(losAngelesZone)).toMillis();//此处时间差包括夏令时差

        long dateTime = date.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(dateTime);
        calendar.add(Calendar.MILLISECOND, Math.toIntExact((millis)));
        Date losAngelesDate=new Date(calendar.getTimeInMillis());
        System.out.println(losAngelesDate);
        return losAngelesDate;
    }

    public static Date setDateTimezoneToUTC(Date date) {
        long dateTime = date.getTime();
        Calendar calendar = Calendar.getInstance();

        calendar.setTimeInMillis(dateTime);
        /** 取得时间偏移量 */
        int zoneOffset = calendar.get(Calendar.ZONE_OFFSET);
        /** 取得夏令时差 */
        int dstOffset = calendar.get(Calendar.DST_OFFSET);
        /** 从本地时间里扣除这些差量，即可以取得UTC时间*/
        calendar.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));
        /** 取得的时间就是UTC标准时间 */
        Date utcDate = new Date(calendar.getTimeInMillis());
        return utcDate;
    }

    public static Date setLosAngelesDateToUTC(Date date) {
        long dateTime = date.getTime();
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeZone(TimeZone.getTimeZone("America/Los_Angeles"));
        calendar.setTimeInMillis(dateTime);
        int zoneOffset = calendar.get(Calendar.ZONE_OFFSET);
        /** 取固定时间偏移量，不包含夏令时差，与服务端同步8小时*/
        calendar.add(Calendar.MILLISECOND, -(zoneOffset));
        Date utcDate = new Date(calendar.getTimeInMillis());
        return utcDate;
    }

    public static void main(String[] args) {
        Date date = new Date();
        System.out.println(date);

        /*Date mirrorLosAngelesDate = toMirrorLosAngelesDate(date);*/

        Date date1 = setLosAngelesDateToUTC(date);
        System.out.println(date1);

        ZoneId losAngelesZone = ZoneId.of("America/Los_Angeles");
        ZoneId newYork = ZoneId.of("America/New_York");
        ZoneId shangHai = ZoneId.of("Asia/Shanghai");
        ZoneId utcZone = ZoneId.of("UTC");
        /*LocalDateTime shangHaiTime = LocalDateTime.ofInstant(date.toInstant(), shangHai);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), losAngelesZone);
        LocalDateTime utc = LocalDateTime.ofInstant(date.toInstant(), utcZone);
        System.out.println("Asia/Shanghai= " + shangHaiTime);
        System.out.println("America/Los_Angeles= " + localDateTime);
        System.out.println("UTC= "+utc);
        Date from = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        LocalDateTime fromTime = LocalDateTime.ofInstant(from.toInstant(), losAngelesZone);
        System.out.println("fromTime="+fromTime);
        Date from1 = Date.from(localDateTime.atZone(losAngelesZone).toInstant());
        System.out.println(from);
        System.out.println(from1);*/

        /*LocalDateTime system = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        LocalDateTime system1 = LocalDateTime.ofInstant(date.toInstant(), newYork);
        System.out.println(system);
        System.out.println(system1);
        System.out.println(system1.atZone(newYork));
        System.out.println(system1.atZone(losAngelesZone));
        long millis = Duration.between(system.atZone(ZoneId.systemDefault()), system.atZone(losAngelesZone)).toMillis();
        long millis1 = Duration.between(system1.atZone(newYork), system1.atZone(losAngelesZone)).toMillis();
        System.out.println(millis);
        System.out.println(millis1);*/
    }
}
