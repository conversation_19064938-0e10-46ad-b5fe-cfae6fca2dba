package com.stanly.admin.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/9/26 13:53
 */
public class ShortIdUtil {

    private static final char[] BASE_62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final String BASE_62_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

    /**
     * id压缩
     * 2022112803531160918504 -> 2BS3rB47bgW
     * @param activityId
     * @return
     */
    public static String compress(String activityId){
        if(activityId.length() != 22){
            return null;
        }
        try {
            String s1 = activityId.substring(0, 14);
            String s2 = activityId.substring(14);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = sdf.parse(s1);
            String base2 = decimalToBase62(Long.parseLong(s2));

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1; // 注意月份从0开始，所以要加1
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int min = calendar.get(Calendar.MINUTE);
            int sec = calendar.get(Calendar.SECOND);

            return String.format("%s%s%s%s%s%s%s", BASE_62[year - 2020], BASE_62[month],
                    BASE_62[day], BASE_62[hour], BASE_62[min], BASE_62[sec], base2);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * id解压
     * 2022112803531160918504 -> 2BS3rB47bgW
     * @param code
     * @return
     */
    public static String uncompress(String code){
        if(code.length() >= 22){
            return code;
        }
        char[] chars1 = code.substring(1, 6).toCharArray();
        StringBuilder result = new StringBuilder();
        result.append(BASE_62_CHARS.indexOf(code.charAt(0)) + 2020);
        for(char c1 : chars1){
            result.append(String.format("%02d",BASE_62_CHARS.indexOf(c1)));
        }
        result.append(String.format("%08d",base62ToDecimal(code.substring(6))));
        return result.toString();
    }

    /**
     * 62进制转10进制
     * @param base32
     * @return
     */
    private static int base62ToDecimal(String base32) {
        int decimal = 0;
        int base = 1;
        for (int i = base32.length() - 1; i >= 0; i--) {
            char c = base32.charAt(i);
            int value = BASE_62_CHARS.indexOf(c);
            decimal += value * base;
            base *= 62;
        }
        return decimal;
    }

    /**
     * 10进制转62进制
     * @param decimal
     * @return
     */
    private static String decimalToBase62(long decimal) {
        StringBuilder result = new StringBuilder();
        while (decimal > 0) {
            int remainder = (int)(decimal % 62L);
            result.insert(0, BASE_62[remainder]);
            decimal /= 62;
        }
        return result.toString();
    }
}
