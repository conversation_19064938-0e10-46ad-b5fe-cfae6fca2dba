package com.stanly.admin.utils.video;

import com.stanly.admin.constants.ErrorMsg;
import com.stanly.admin.exception.AdminException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.info.MultimediaInfo;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/9/8 19:06
 **/
@Slf4j
public class VideoUtil {


    public static VideoInfo getVideoInfo(MultipartFile file){
        try {
            String prefix = UUID.randomUUID().toString().replace("-", "");
            String originalFilename = file.getOriginalFilename();
            String contentType = file.getContentType();
            File tempFile = File.createTempFile(prefix,originalFilename);
            file.transferTo(tempFile);
            VideoInfo videoInfo = getVideoInfo(tempFile,contentType);
            return videoInfo;
        } catch (Exception e) {
            log.error("读取文件失败",e);
            throw new AdminException(ErrorMsg.Read_File_Error);
        }
    }


    /**
     * 获取时间信息
     * @param file
     * @return
     */
    public static VideoInfo getVideoInfo(File file,String contentType) {
        VideoInfo videoInfo = new VideoInfo();
        // 视频时长
        long time;
        try {
            MultimediaObject media = new MultimediaObject(file);
            MultimediaInfo info = media.getInfo();
            // 时长，毫秒级
            long duration = info.getDuration();
            // 毫秒级时长转化为秒
            BigDecimal bigDecimal1 = new BigDecimal(duration);
            BigDecimal bigDecimal2 = new BigDecimal(1000);
            // 四舍五入，只保留整数
            time = bigDecimal1.divide(bigDecimal2, 0, RoundingMode.HALF_UP).longValue();

            Integer width = info.getVideo().getSize().getWidth();
            Integer height = info.getVideo().getSize().getHeight();
            videoInfo.setTime(Math.toIntExact(time));
            videoInfo.setWidth(width);
            videoInfo.setHeight(height);
            videoInfo.setTemp(file);
            return videoInfo;
        } catch (Exception e) {
            log.error("获取视频时长失败", e);
            throw new AdminException(ErrorMsg.Read_File_Error);
        }
    }
}