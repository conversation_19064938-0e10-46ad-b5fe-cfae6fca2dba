package com.stanly.admin.config;

import io.swagger.v3.core.converter.AnnotatedType;
import io.swagger.v3.core.converter.ModelConverter;
import io.swagger.v3.core.converter.ModelConverterContext;
import io.swagger.v3.core.converter.ModelConverters;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;

import java.util.Iterator;

/**
 * api页面 /doc.html
 * <AUTHOR>
 * @date 2018-11-23
 */
@Configuration
public class SwaggerConfig {

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${swagger.enabled}")
    private Boolean enabled;

    @Bean
    public OpenAPI openApiBase() {
        Server localServer = new Server();
        localServer.setUrl("/");
        localServer.setDescription("Local environment");

        Info info = new Info()
                .title("ELADMIN 接口文档")
                .version("2.7")
                .description("一个简单且易上手的 Spring boot 后台管理框架");

        return new OpenAPI()
                .info(info)
                .addServersItem(localServer)
                .components(new Components()
                        .addSecuritySchemes("bearerToken", securityScheme()))
                .addSecurityItem(new SecurityRequirement().addList("bearerToken"));
    }

    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name(tokenHeader);
    }

    @Bean
    @Lazy
    public GroupedOpenApi apiV1() {
        return GroupedOpenApi.builder()
                .group("API V1")
                .pathsToMatch("/**")
                .build();
                
    }

    @Bean
    public ModelConverters pageableConvention() {
        ModelConverters modelConverters = new ModelConverters();
        modelConverters.addConverter(new PageableConverter());
        return modelConverters;
    }

    public class PageableConverter implements ModelConverter {
        @Override
        public io.swagger.v3.oas.models.media.Schema resolve(AnnotatedType type, ModelConverterContext context, Iterator<ModelConverter> chain) {
            if (type.getType().equals(Pageable.class)) {
                return new io.swagger.v3.oas.models.media.Schema().name("Page")
                        .addProperties("page", new io.swagger.v3.oas.models.media.Schema().type("integer").description("页码 (0..N)"))
                        .addProperties("size", new io.swagger.v3.oas.models.media.Schema().type("integer").description("每页显示的数目"))
                        .addProperties("sort", new io.swagger.v3.oas.models.media.Schema().type("array").items(new io.swagger.v3.oas.models.media.Schema().type("string"))
                                .description("以下列格式排序标准：property[,asc | desc]。 默认排序顺序为升序。 支持多种排序条件：如：id,asc"));
            }
            return null;
        }
    }
}