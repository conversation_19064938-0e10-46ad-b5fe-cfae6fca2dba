/*
 Navicat Premium Data Transfer

 Source Server         : drex-dev
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : rm-t4nvmql4d7h5he6x6yo.mysql.singapore.rds.aliyuncs.com:3306
 Source Schema         : eladmin

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 12/05/2025 11:08:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for code_column_config
-- ----------------------------
DROP TABLE IF EXISTS `code_column_config`;
CREATE TABLE `code_column_config` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `table_name` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `column_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `column_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `dict_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extra` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `form_show` bit(1) DEFAULT NULL,
  `form_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `key_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `list_show` bit(1) DEFAULT NULL,
  `not_null` bit(1) DEFAULT NULL,
  `query_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_annotation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`column_id`) USING BTREE,
  KEY `idx_table_name` (`table_name`)
) ENGINE=InnoDB AUTO_INCREMENT=140 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='代码生成字段信息存储';

-- ----------------------------
-- Records of code_column_config
-- ----------------------------
BEGIN;
INSERT INTO `code_column_config` VALUES (1, 'task_config', 'id', 'varchar', NULL, '', b'0', NULL, 'PRI', b'0', b'0', NULL, '数据主键', NULL);
INSERT INTO `code_column_config` VALUES (2, 'task_config', 'saas_id', 'varchar', 'saas_id', '', b'1', 'Select', '', b'1', b'1', '=', 'SaasId', NULL);
INSERT INTO `code_column_config` VALUES (3, 'task_config', 'task_id', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', 'Like', '任务id', NULL);
INSERT INTO `code_column_config` VALUES (4, 'task_config', 'group_id', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', '', '分组id', NULL);
INSERT INTO `code_column_config` VALUES (5, 'task_config', 'is_group', 'varchar', 'boolean', '', b'1', 'Select', '', b'0', b'0', NULL, '是否在分组内(0 否 1 是)', NULL);
INSERT INTO `code_column_config` VALUES (6, 'task_config', 'show_list', 'varchar', 'boolean', '', b'1', 'Select', '', b'0', b'0', NULL, '是否列表展示(0 否 1 是)', NULL);
INSERT INTO `code_column_config` VALUES (7, 'task_config', 'ledger_title', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '积分流水描述', NULL);
INSERT INTO `code_column_config` VALUES (8, 'task_config', 'title', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', 'Like', '任务标题', NULL);
INSERT INTO `code_column_config` VALUES (9, 'task_config', 'title_app', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务标题(APP)', NULL);
INSERT INTO `code_column_config` VALUES (10, 'task_config', 'title_pc', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务标题(PC)', NULL);
INSERT INTO `code_column_config` VALUES (11, 'task_config', 'title_desc_app_normal', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述(APP非会员)', NULL);
INSERT INTO `code_column_config` VALUES (12, 'task_config', 'title_desc_app_l1', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述(APP会员L1)', NULL);
INSERT INTO `code_column_config` VALUES (13, 'task_config', 'title_desc_pc_normal', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述(PC非会员)', NULL);
INSERT INTO `code_column_config` VALUES (14, 'task_config', 'title_desc_pc_l1', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述(PC会员L1)', NULL);
INSERT INTO `code_column_config` VALUES (15, 'task_config', 'label_name', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务角标内容', NULL);
INSERT INTO `code_column_config` VALUES (16, 'task_config', 'label_color', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务角标颜色', NULL);
INSERT INTO `code_column_config` VALUES (17, 'task_config', 'status', 'varchar', 'task_status', '', b'1', 'Select', '', b'1', b'0', '=', '任务状态(ACTIVE、DISABLE)', NULL);
INSERT INTO `code_column_config` VALUES (18, 'task_config', 'start_time', 'datetime', NULL, '', b'1', 'Date', '', b'1', b'0', NULL, '任务开始时间，utc毫秒时间戳', NULL);
INSERT INTO `code_column_config` VALUES (19, 'task_config', 'end_time', 'datetime', NULL, '', b'1', 'Date', '', b'1', b'0', NULL, '任务结束时间，utc毫秒时间戳', NULL);
INSERT INTO `code_column_config` VALUES (20, 'task_config', 'code', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', '=', '任务事件编号', NULL);
INSERT INTO `code_column_config` VALUES (21, 'task_config', 'show_code', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '前端需要显示的事件，目前主要映射icon', NULL);
INSERT INTO `code_column_config` VALUES (22, 'task_config', 'limit_count_normal', 'int', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务次数上限(非会员)', NULL);
INSERT INTO `code_column_config` VALUES (23, 'task_config', 'limit_count_l1', 'int', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务次数上限(会员L1)', NULL);
INSERT INTO `code_column_config` VALUES (24, 'task_config', 'task_list_image', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务列表图片', NULL);
INSERT INTO `code_column_config` VALUES (25, 'task_config', 'task_detail_image', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务详情图片', NULL);
INSERT INTO `code_column_config` VALUES (26, 'task_config', 'reward_frequency', 'int', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '发奖频率，每完成n次任务，发一次奖', NULL);
INSERT INTO `code_column_config` VALUES (27, 'task_config', 'cycle', 'varchar', 'task_cycle', '', b'1', 'Select', '', b'0', b'0', NULL, '任务刷新周期', NULL);
INSERT INTO `code_column_config` VALUES (28, 'task_config', 'progress_type', 'varchar', 'progress_type', '', b'1', 'Select', '', b'0', b'0', NULL, '进度计算方式', NULL);
INSERT INTO `code_column_config` VALUES (29, 'task_config', 'reward_form', 'varchar', 'reward_form', '', b'1', 'Select', '', b'0', b'0', NULL, '奖品计算方式', NULL);
INSERT INTO `code_column_config` VALUES (30, 'task_config', 'provide_type', 'varchar', 'provide_type', '', b'1', 'Select', '', b'0', b'0', NULL, '积分领取方式', NULL);
INSERT INTO `code_column_config` VALUES (31, 'task_config', 'reward_type', 'varchar', 'reward_type', '', b'1', 'Select', '', b'0', b'0', NULL, '奖励类型', NULL);
INSERT INTO `code_column_config` VALUES (32, 'task_config', 'reward_amount', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '奖励数量', NULL);
INSERT INTO `code_column_config` VALUES (36, 'task_config', 'order', 'int', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务排序', NULL);
INSERT INTO `code_column_config` VALUES (37, 'task_config', 'domain', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务所属模块twitter、discord', NULL);
INSERT INTO `code_column_config` VALUES (38, 'task_config', 'twitter_follow', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, 'twitter被关注的人', NULL);
INSERT INTO `code_column_config` VALUES (39, 'task_config', 'twitter_keyword', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '发帖包含的关键字', NULL);
INSERT INTO `code_column_config` VALUES (40, 'task_config', 'twitter_random_append_text', 'text', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '贴文追加的文案', NULL);
INSERT INTO `code_column_config` VALUES (41, 'task_config', 'twitter_random_text', 'text', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '贴文替换的文案', NULL);
INSERT INTO `code_column_config` VALUES (42, 'task_config', 'twitter_kols', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '回复任务要求指定的用户', NULL);
INSERT INTO `code_column_config` VALUES (43, 'task_config', 'twitter_username', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, 'twitter用户名包含的关键字', NULL);
INSERT INTO `code_column_config` VALUES (44, 'task_config', 'discord_guild', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, 'discord服务器id', NULL);
INSERT INTO `code_column_config` VALUES (45, 'task_config', 'discord_guild_role', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '拥有discord某个角色', NULL);
INSERT INTO `code_column_config` VALUES (46, 'task_config', 'vip_level', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务要求的vip等级，+0: 普通用户以上，1: 会员专享', NULL);
INSERT INTO `code_column_config` VALUES (47, 'task_config', 'btn', 'int', 'btn_show', '', b'1', 'Select', '', b'0', b'0', NULL, '前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮', NULL);
INSERT INTO `code_column_config` VALUES (48, 'task_config', 'show_progress', 'varchar', 'boolean', '', b'1', 'Select', '', b'0', b'0', NULL, '是否显示任务showProgress进度条', NULL);
INSERT INTO `code_column_config` VALUES (49, 'task_config', 'url', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务的详情页', NULL);
INSERT INTO `code_column_config` VALUES (50, 'task_config', 'link_name', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述下的按钮文字', NULL);
INSERT INTO `code_column_config` VALUES (51, 'task_config', 'link_url', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务描述下的按钮链接', NULL);
INSERT INTO `code_column_config` VALUES (52, 'task_config', 'channel', 'varchar', 'task_channel', '', b'1', 'Select', '', b'0', b'0', NULL, '任务所属渠道，pc | app', NULL);
INSERT INTO `code_column_config` VALUES (53, 'task_config', 'position', 'varchar', NULL, '', b'1', 'Input', '', b'0', b'0', NULL, '任务需要显示的页面位置', NULL);
INSERT INTO `code_column_config` VALUES (54, 'task_config', 'call_register', 'varchar', 'boolean', '', b'1', 'Select', '', b'0', b'0', NULL, '是否回调注册任务，默认true，库里null表示true', NULL);
INSERT INTO `code_column_config` VALUES (55, 'task_config', 'task_status_condition', 'varchar', 'task_status_condition', '', b'1', 'Select', '', b'0', b'0', NULL, '任务状态判断，默认任务表状态twitterAuth：twitter授权过discordAuth: discord授权过', NULL);
INSERT INTO `code_column_config` VALUES (56, 'task_config', 'skip_verification', 'varchar', 'boolean', '', b'1', 'Select', '', b'0', b'0', NULL, '是否跳过验证', NULL);
INSERT INTO `code_column_config` VALUES (57, 'task_config', 'client_type', 'varchar', 'client_type', '', b'1', 'Select', '', b'0', b'0', NULL, '任务所属客户端类型，android｜ios', NULL);
INSERT INTO `code_column_config` VALUES (58, 'task_icon', 'id', 'varchar', NULL, '', b'0', NULL, 'PRI', b'0', b'1', NULL, '', NULL);
INSERT INTO `code_column_config` VALUES (59, 'task_icon', 'saas_id', 'varchar', 'saas_id', '', b'1', 'Select', '', b'1', b'0', '=', '', NULL);
INSERT INTO `code_column_config` VALUES (60, 'task_icon', 'code', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', 'Like', '', NULL);
INSERT INTO `code_column_config` VALUES (61, 'task_icon', 'icon', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `code_column_config` VALUES (62, 'task_code_config', 'id', 'varchar', NULL, '', b'0', 'Input', 'PRI', b'1', b'0', NULL, '数据主键', NULL);
INSERT INTO `code_column_config` VALUES (63, 'task_code_config', 'code', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'1', '=', '任务编号', NULL);
INSERT INTO `code_column_config` VALUES (64, 'task_code_config', 'main_code', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'1', '=', '主任务编号', NULL);
INSERT INTO `code_column_config` VALUES (65, 'task_code_config', 'inc', 'int', NULL, '', b'1', 'Input', '', b'1', b'1', '=', '增量值', NULL);
INSERT INTO `code_column_config` VALUES (66, 'task_code_config', 'desc', 'varchar', NULL, '', b'0', 'Input', '', b'1', b'1', '=', '任务编号描述', NULL);
INSERT INTO `code_column_config` VALUES (67, 'task_code_config', 'create_time', 'date', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '创建时间', 'CreationTimestamp');
INSERT INTO `code_column_config` VALUES (68, 'task_code_config', 'modified_time', 'date', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '修改时间', 'UpdateTimestamp');
INSERT INTO `code_column_config` VALUES (69, 'task_code', 'id', 'varchar', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '数据主键', NULL);
INSERT INTO `code_column_config` VALUES (70, 'task_code', 'code', 'varchar', NULL, '', b'1', 'Input', 'PRI', b'1', b'1', '=', '任务事件', NULL);
INSERT INTO `code_column_config` VALUES (71, 'task_code', 'main_code', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'1', '=', '主任务事件', NULL);
INSERT INTO `code_column_config` VALUES (72, 'task_code', 'inc', 'int', NULL, '', b'1', 'Input', '', b'1', b'1', '=', '增量值', NULL);
INSERT INTO `code_column_config` VALUES (73, 'task_code', 'desc', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '任务事件描述', NULL);
INSERT INTO `code_column_config` VALUES (74, 'task_code', 'create_time', 'datetime', NULL, '', b'0', '', '', b'1', b'0', NULL, '创建时间', 'CreationTimestamp');
INSERT INTO `code_column_config` VALUES (75, 'task_code', 'modified_time', 'datetime', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '修改时间', 'UpdateTimestamp');
INSERT INTO `code_column_config` VALUES (76, 'task_code', 'split_code', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', '=', '拆分子事件', NULL);
INSERT INTO `code_column_config` VALUES (77, 'sys_user', 'user_id', 'bigint', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, 'ID', NULL);
INSERT INTO `code_column_config` VALUES (78, 'sys_user', 'dept_id', 'bigint', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '部门名称', NULL);
INSERT INTO `code_column_config` VALUES (79, 'sys_user', 'username', 'varchar', NULL, '', b'1', NULL, 'UNI', b'1', b'0', NULL, '用户名', NULL);
INSERT INTO `code_column_config` VALUES (80, 'sys_user', 'nick_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '昵称', NULL);
INSERT INTO `code_column_config` VALUES (81, 'sys_user', 'gender', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '性别', NULL);
INSERT INTO `code_column_config` VALUES (82, 'sys_user', 'phone', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '手机号码', NULL);
INSERT INTO `code_column_config` VALUES (83, 'sys_user', 'email', 'varchar', NULL, '', b'1', NULL, 'UNI', b'1', b'0', NULL, '邮箱', NULL);
INSERT INTO `code_column_config` VALUES (84, 'sys_user', 'avatar_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '头像地址', NULL);
INSERT INTO `code_column_config` VALUES (85, 'sys_user', 'avatar_path', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '头像真实路径', NULL);
INSERT INTO `code_column_config` VALUES (86, 'sys_user', 'password', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '密码', NULL);
INSERT INTO `code_column_config` VALUES (87, 'sys_user', 'is_admin', 'bit', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '是否为admin账号', NULL);
INSERT INTO `code_column_config` VALUES (88, 'sys_user', 'enabled', 'bit', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '状态：1启用、0禁用', NULL);
INSERT INTO `code_column_config` VALUES (89, 'sys_user', 'create_by', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '创建者', NULL);
INSERT INTO `code_column_config` VALUES (90, 'sys_user', 'update_by', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '更新者', NULL);
INSERT INTO `code_column_config` VALUES (91, 'sys_user', 'pwd_reset_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '修改密码的时间', NULL);
INSERT INTO `code_column_config` VALUES (92, 'sys_user', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '创建日期', NULL);
INSERT INTO `code_column_config` VALUES (93, 'sys_user', 'update_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '更新时间', NULL);
INSERT INTO `code_column_config` VALUES (94, 'task_config', 'osp_call_back', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '是否需要回调osp', NULL);
INSERT INTO `code_column_config` VALUES (95, 'task_config', 'task_type', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, 'osp事件类型', NULL);
INSERT INTO `code_column_config` VALUES (96, 'task_whitelist', 'id', 'varchar', NULL, '', b'0', NULL, 'PRI', b'0', b'0', NULL, 'ID', NULL);
INSERT INTO `code_column_config` VALUES (97, 'task_whitelist', 'saas_id', 'varchar', 'saas_id', '', b'1', 'Select', '', b'1', b'1', '=', 'SaaS ID', NULL);
INSERT INTO `code_column_config` VALUES (98, 'task_whitelist', 'flag', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户id', NULL);
INSERT INTO `code_column_config` VALUES (99, 'task_whitelist', 'remark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '备注', NULL);
INSERT INTO `code_column_config` VALUES (100, 'task_config', 'rewards', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '奖励数量', NULL);
INSERT INTO `code_column_config` VALUES (101, 'task_config', 'publish_time', 'datetime', NULL, '', b'1', 'Date', '', b'1', b'0', '=', '计划发布时间', NULL);
INSERT INTO `code_column_config` VALUES (102, 'task_config', 'show_reward_amount', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `code_column_config` VALUES (103, 'task_config', 'app_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, 'appId', NULL);
INSERT INTO `code_column_config` VALUES (104, 'task_config', 'chain_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, 'chainId', NULL);
INSERT INTO `code_column_config` VALUES (105, 'task_config', 'discord_auth_url_app', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, 'discord授权地址', NULL);
INSERT INTO `code_column_config` VALUES (106, 'task_config', 'discord_auth_url_pc', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, 'discord pcs授权地址', NULL);
INSERT INTO `code_column_config` VALUES (107, 'task_config', 'last_post', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '查最近帖文', NULL);
INSERT INTO `code_column_config` VALUES (123, 'task_config', 'check_reward', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '校验任务是否执行', NULL);
INSERT INTO `code_column_config` VALUES (124, 'task_config', 'reward_currency', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, 'reward_currency', NULL);
INSERT INTO `code_column_config` VALUES (125, 'information', 'id', 'int', NULL, '', b'0', NULL, 'PRI', b'1', b'0', '=', '主键', NULL);
INSERT INTO `code_column_config` VALUES (126, 'information', 'type', 'varchar', 'information_type', '', b'1', 'Select', '', b'1', b'1', '=', '类型 podcast、dapp', NULL);
INSERT INTO `code_column_config` VALUES (127, 'information', 'name', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', 'Like', '名称', NULL);
INSERT INTO `code_column_config` VALUES (128, 'information', 'logo', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', NULL, 'logo链接', NULL);
INSERT INTO `code_column_config` VALUES (129, 'information', 'title', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'1', 'Like', '标题', NULL);
INSERT INTO `code_column_config` VALUES (130, 'information', 'sub_title', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'0', NULL, '副标题', NULL);
INSERT INTO `code_column_config` VALUES (131, 'information', 'summary', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'1', NULL, '摘要', NULL);
INSERT INTO `code_column_config` VALUES (132, 'information', 'content', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'0', NULL, '内容', NULL);
INSERT INTO `code_column_config` VALUES (133, 'information', 'image', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', NULL, '图片链接', NULL);
INSERT INTO `code_column_config` VALUES (134, 'information', 'link', 'varchar', NULL, '', b'1', 'Input', '', b'1', b'0', NULL, '外部链接', NULL);
INSERT INTO `code_column_config` VALUES (135, 'information', 'category', 'varchar', 'information_ category', '', b'1', 'Select', '', b'1', b'0', '=', '分类 [GameFi, SocialFi]', NULL);
INSERT INTO `code_column_config` VALUES (136, 'information', 'is_recommend', 'varchar', 'boolean', '', b'1', 'Select', '', b'1', b'0', '=', '推荐置顶', NULL);
INSERT INTO `code_column_config` VALUES (137, 'information', 'sort', 'int', NULL, '', b'1', 'Input', '', b'1', b'0', NULL, '排序值', NULL);
INSERT INTO `code_column_config` VALUES (138, 'information', 'created', 'bigint', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '创建时间', NULL);
INSERT INTO `code_column_config` VALUES (139, 'information', 'modified', 'bigint', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '修改时间', NULL);
INSERT INTO `code_column_config` VALUES (160, 'information', 'organizer', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'0', NULL, '活动举办方', NULL);
INSERT INTO `code_column_config` VALUES (161, 'information', 'location', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'0', NULL, '活动地点', NULL);
INSERT INTO `code_column_config` VALUES (162, 'information', 'activity_start_time', 'bigint', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '活动开始时间', NULL);
INSERT INTO `code_column_config` VALUES (163, 'information', 'activity_end_time', 'bigint', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '活动结束时间', NULL);
INSERT INTO `code_column_config` VALUES (164, 'information', 'position', 'varchar', NULL, '', b'1', 'Textarea', '', b'1', b'0', NULL, '展示位置', NULL);




COMMIT;

-- ----------------------------
-- Table structure for code_gen_config
-- ----------------------------
DROP TABLE IF EXISTS `code_gen_config`;
CREATE TABLE `code_gen_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表名',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '作者',
  `cover` bit(1) DEFAULT NULL COMMENT '是否覆盖',
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模块名称',
  `pack` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '至于哪个包下',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前端代码生成的路径',
  `api_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前端Api文件路径',
  `prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表前缀',
  `api_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '接口名称',
  PRIMARY KEY (`config_id`) USING BTREE,
  KEY `idx_table_name` (`table_name`(100))
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='代码生成器配置';

-- ----------------------------
-- Records of code_gen_config
-- ----------------------------
BEGIN;
INSERT INTO `code_gen_config` VALUES (9, 'task_config', 'wangyuchen', b'1', 'eladmin-system', 'com.stanly.quests.task.config.temp', '/Users/<USER>/wangyuchen/工作/IdeaProjects/qulianProjects/manage-web/src/views/quests/task/config', '/Users/<USER>/wangyuchen/工作/IdeaProjects/qulianProjects/manage-web/src/api', NULL, '任务配置接口');
INSERT INTO `code_gen_config` VALUES (10, 'task_icon', 'lijialin', b'1', 'eladmin-system', 'com.stanly.quests.task.icon', 'quests/task/icon', 'quests/task/icon/', NULL, 'TaskIconController');
INSERT INTO `code_gen_config` VALUES (11, 'task_code_config', 'wangyuchen', b'1', 'eladmin-system', 'quests.taskcode.config', '/Users/<USER>/wangyuchen/工作/IdeaProjects/qulianProjects/manage-web/src/api/quests/taskcode/config', '/Users/<USER>/wangyuchen/工作/IdeaProjects/qulianProjects/manage-web/src/api', NULL, '配置编码配置表');
INSERT INTO `code_gen_config` VALUES (12, 'task_code', 'panl', b'1', 'eladmin-system', 'com.stanly.quests.task.taskCode', '/Users/<USER>/IdeaProjects/manage-web/src/views/quests/task/code', '/Users/<USER>/IdeaProjects/manage-web/src/api', NULL, '任务事件配置');
INSERT INTO `code_gen_config` VALUES (13, 'task_whitelist', 'jialin.li', b'0', 'eladmin-system', 'template', '/white', '/white/', NULL, 'TaskWhite');
INSERT INTO `code_gen_config` VALUES (15, 'information', 'wangyuchen', b'1', 'eladmin-system', 'com.stanly.drex.core.information', 'manage-web/src/views/drex/information', 'manage-web/src/api', NULL, '项目信息配置');
INSERT INTO `code_gen_config` VALUES (16,'notice','wangyuchen',b'1','eladmin-system','com.stanly.drex.notify','manage-web/src/views/drex/notify','manage-web/src/api',null,'通知消息配置');
INSERT INTO `code_gen_config` VALUES (17,'rexy_config','jialin.li',b'1','eladmin-system','com.stanly.drex.rexy','drex/rexy','drex/rexy/',null,'RexyConfigController');
COMMIT;

-- ----------------------------
-- Table structure for information
-- ----------------------------
DROP TABLE IF EXISTS `information`;
CREATE TABLE `information` (
  `id` varchar(255) NOT NULL COMMENT '主键',
  `type` varchar(255) DEFAULT NULL COMMENT '类型 podcast、dapp',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `logo` varchar(255) DEFAULT NULL COMMENT 'logo链接',
  `title` varchar(2000) DEFAULT NULL COMMENT '标题',
  `sub_title` varchar(2000) DEFAULT NULL COMMENT '副标题',
  `summary` varchar(3000) DEFAULT NULL COMMENT '摘要',
  `content` varchar(5000) DEFAULT NULL COMMENT '内容',
  `image` varchar(2000) DEFAULT NULL COMMENT '图片链接',
  `link` varchar(2000) DEFAULT NULL COMMENT '外部链接',
  `category` varchar(1024) DEFAULT NULL COMMENT '分类 [GameFi, SocialFi]',
  `is_recommend` varchar(10) DEFAULT NULL COMMENT '推荐置顶',
  `status` int DEFAULT NULL COMMENT '发布状态 0 未发布 1 已发布',
  `sort` int DEFAULT NULL COMMENT '排序值',
  `tag` varchar(255) DEFAULT NULL COMMENT '来源icon',
  `date` bigint DEFAULT NULL COMMENT '发布时间',
  `organizer` varchar(255) DEFAULT NULL COMMENT '活动举办方',
  `location` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `position` varchar(255) DEFAULT NULL COMMENT '展示位置',
  `activity_start_time` bigint DEFAULT NULL COMMENT '活动开始时间',
  `activity_end_time` bigint DEFAULT NULL COMMENT '活动结束时间',
  `is_reward` varchar(10) DEFAULT NULL COMMENT '是否有奖励',
  `reward_amount` varchar(255) DEFAULT NULL COMMENT '奖励数量',
  `reward_rules` varchar(255) DEFAULT NULL COMMENT '奖励规则',
  `created` bigint DEFAULT NULL COMMENT '创建时间',
  `modified` bigint DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='项目信息表';

-- ----------------------------
-- Records of information
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_app
-- ----------------------------
DROP TABLE IF EXISTS `mnt_app`;
CREATE TABLE `mnt_app` (
  `app_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名称',
  `upload_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上传目录',
  `deploy_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部署路径',
  `backup_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备份路径',
  `port` int DEFAULT NULL COMMENT '应用端口',
  `start_script` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '启动脚本',
  `deploy_script` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部署脚本',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='应用管理';

-- ----------------------------
-- Records of mnt_app
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_database
-- ----------------------------
DROP TABLE IF EXISTS `mnt_database`;
CREATE TABLE `mnt_database` (
  `db_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `jdbc_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'jdbc连接',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账号',
  `pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`db_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据库管理';

-- ----------------------------
-- Records of mnt_database
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_deploy
-- ----------------------------
DROP TABLE IF EXISTS `mnt_deploy`;
CREATE TABLE `mnt_deploy` (
  `deploy_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` bigint DEFAULT NULL COMMENT '应用编号',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`deploy_id`) USING BTREE,
  KEY `FK6sy157pseoxx4fmcqr1vnvvhy` (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='部署管理';

-- ----------------------------
-- Records of mnt_deploy
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_deploy_history
-- ----------------------------
DROP TABLE IF EXISTS `mnt_deploy_history`;
CREATE TABLE `mnt_deploy_history` (
  `history_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'ID',
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `deploy_date` datetime NOT NULL COMMENT '部署日期',
  `deploy_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部署用户',
  `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务器IP',
  `deploy_id` bigint DEFAULT NULL COMMENT '部署编号',
  PRIMARY KEY (`history_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='部署历史管理';

-- ----------------------------
-- Records of mnt_deploy_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_deploy_server
-- ----------------------------
DROP TABLE IF EXISTS `mnt_deploy_server`;
CREATE TABLE `mnt_deploy_server` (
  `deploy_id` bigint NOT NULL COMMENT '部署ID',
  `server_id` bigint NOT NULL COMMENT '服务ID',
  PRIMARY KEY (`deploy_id`,`server_id`) USING BTREE,
  KEY `FKeaaha7jew9a02b3bk9ghols53` (`server_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='应用与服务器关联';

-- ----------------------------
-- Records of mnt_deploy_server
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for mnt_server
-- ----------------------------
DROP TABLE IF EXISTS `mnt_server`;
CREATE TABLE `mnt_server` (
  `server_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '账号',
  `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名称',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `port` int DEFAULT NULL COMMENT '端口',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`server_id`) USING BTREE,
  KEY `idx_ip` (`ip`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='服务器管理';

-- ----------------------------
-- Records of mnt_server
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for student
-- ----------------------------
DROP TABLE IF EXISTS `student`;
CREATE TABLE `student` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `age` int DEFAULT NULL COMMENT '年龄',
  `gender` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性别',
  `score` float(18,2) DEFAULT NULL COMMENT '分数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生表';

-- ----------------------------
-- Records of student
-- ----------------------------
BEGIN;
INSERT INTO `student` VALUES (2, 'wangyuchen', 78, 'male', 30.00);
COMMIT;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` bigint DEFAULT NULL COMMENT '上级部门',
  `sub_count` int DEFAULT '0' COMMENT '子部门数目',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `dept_sort` int DEFAULT '999' COMMENT '排序',
  `enabled` bit(1) NOT NULL COMMENT '状态',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE,
  KEY `inx_pid` (`pid`),
  KEY `inx_enabled` (`enabled`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='部门';

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
BEGIN;
INSERT INTO `sys_dept` VALUES (2, 7, 1, '研发部', 3, b'1', 'admin', 'admin', '2019-03-25 09:15:32', '2020-08-02 14:48:47');
INSERT INTO `sys_dept` VALUES (5, 7, 0, '运维部', 4, b'1', 'admin', 'admin', '2019-03-25 09:20:44', '2020-05-17 14:27:27');
INSERT INTO `sys_dept` VALUES (6, 8, 0, '测试部', 6, b'1', 'admin', 'admin', '2019-03-25 09:52:18', '2020-06-08 11:59:21');
INSERT INTO `sys_dept` VALUES (7, NULL, 2, '华南分部', 0, b'1', 'admin', 'admin', '2019-03-25 11:04:50', '2020-06-08 12:08:56');
INSERT INTO `sys_dept` VALUES (8, NULL, 2, '华北分部', 1, b'1', 'admin', 'admin', '2019-03-25 11:04:53', '2020-05-14 12:54:00');
INSERT INTO `sys_dept` VALUES (15, 8, 0, 'UI部门', 7, b'1', 'admin', 'admin', '2020-05-13 22:56:53', '2020-05-14 12:54:13');
INSERT INTO `sys_dept` VALUES (17, 2, 0, '研发一组', 999, b'1', 'admin', 'admin', '2020-08-02 14:49:07', '2020-08-02 14:49:07');
COMMIT;

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dict_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据字典';

-- ----------------------------
-- Records of sys_dict
-- ----------------------------
BEGIN;
INSERT INTO `sys_dict` VALUES (1, 'user_status', '用户状态', NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict` VALUES (4, 'dept_status', '部门状态', NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict` VALUES (5, 'job_status', '岗位状态', NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict` VALUES (7, 'boolean', '布尔值', 'admin', 'admin', '2024-07-31 15:40:15', '2024-07-31 15:40:15');
INSERT INTO `sys_dict` VALUES (8, 'task_status', '任务状态', 'admin', 'admin', '2024-07-31 15:42:20', '2024-07-31 15:42:20');
INSERT INTO `sys_dict` VALUES (9, 'task_cycle', '任务周期', 'admin', 'admin', '2024-07-31 15:44:26', '2024-07-31 15:44:35');
INSERT INTO `sys_dict` VALUES (10, 'progress_type', '任务进度计算方式', 'admin', 'admin', '2024-07-31 15:46:24', '2024-07-31 15:46:24');
INSERT INTO `sys_dict` VALUES (11, 'reward_form', '奖品计算方式', 'admin', 'admin', '2024-07-31 15:47:51', '2024-07-31 15:50:24');
INSERT INTO `sys_dict` VALUES (12, 'provide_type', '奖品领取方式', 'admin', 'admin', '2024-07-31 15:49:20', '2024-07-31 15:50:43');
INSERT INTO `sys_dict` VALUES (13, 'btn_show', '按钮显示', 'admin', 'admin', '2024-07-31 16:04:35', '2024-07-31 16:04:44');
INSERT INTO `sys_dict` VALUES (14, 'app_id', 'appId', 'admin', 'admin', '2024-07-31 17:06:23', '2024-07-31 17:06:23');
INSERT INTO `sys_dict` VALUES (15, 'reward_type', '奖励类型', 'admin', 'admin', '2024-08-02 17:09:14', '2024-08-02 17:09:14');
INSERT INTO `sys_dict` VALUES (16, 'task_channel', '任务渠道', 'admin', 'admin', '2024-08-02 17:09:59', '2024-08-02 17:09:59');
INSERT INTO `sys_dict` VALUES (17, 'task_status_condition', '任务状态判断条件', 'admin', 'admin', '2024-08-02 17:11:11', '2024-08-02 17:11:11');
INSERT INTO `sys_dict` VALUES (18, 'client_type', '客户端类型', 'admin', 'admin', '2024-08-02 17:12:31', '2024-08-02 17:12:31');
INSERT INTO `sys_dict` VALUES (19, 'domain', '模块', 'admin', 'admin', '2024-08-05 20:59:11', '2024-08-05 20:59:11');
INSERT INTO `sys_dict` VALUES (20, 'event_code', '任务事件', 'admin', 'admin', '2024-11-15 17:24:11', '2024-12-12 06:37:14');
INSERT INTO `sys_dict` VALUES (21, 'task_type', NULL, 'admin', 'admin', '2024-11-18 16:12:53', '2024-11-18 16:12:53');
INSERT INTO `sys_dict` VALUES (23, 'intboolean', '数字表示boolean', 'admin', 'admin', '2024-11-23 09:37:12', '2024-11-23 09:37:12');
INSERT INTO `sys_dict` VALUES (24, 'activity_code', '活动编号', NULL, NULL, '2025-04-02 15:02:21', '2025-04-02 15:02:21');
INSERT INTO `sys_dict` VALUES (25, 'lottery_pool_code', '奖池编号', NULL, NULL, '2025-04-02 15:05:50', '2025-04-02 15:26:09');
INSERT INTO `sys_dict` VALUES (26, 'information_type', '项目类型', NULL, NULL, '2025-05-06 09:37:10', '2025-05-06 09:37:10');
INSERT INTO `sys_dict` VALUES (27, 'information_category', '项目分类', NULL, NULL, '2025-05-06 09:41:47', '2025-05-06 09:55:33');
INSERT INTO `sys_dict` VALUES (28, 'project_component', '组件', NULL, NULL, '2025-05-07 09:06:33', '2025-05-07 09:06:33');
INSERT IGNORE INTO `eladmin`.`sys_dict` (`dict_id`, `name`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(29, 'notify_status', '通知状态', null, null, '2025-05-19 06:00:40', '2025-05-19 06:00:40');
INSERT IGNORE INTO `eladmin`.`sys_dict` (`dict_id`, `name`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(30, 'customer_group', '用户分组', null, null, '2025-05-19 06:01:32', '2025-05-19 06:01:32');
INSERT IGNORE INTO `eladmin`.`sys_dict` (`dict_id`, `name`, `description`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(31, 'saas_id', null, null, null, '2025-05-20 08:54:41', '2025-05-20 08:54:41');
COMMIT;

-- ----------------------------
-- Table structure for sys_dict_detail
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_detail`;
CREATE TABLE `sys_dict_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dict_id` bigint DEFAULT NULL COMMENT '字典id',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典标签',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '字典值',
  `dict_sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`detail_id`) USING BTREE,
  KEY `FK5tpkputc6d9nboxojdbgnpmyb` (`dict_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=166 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='数据字典详情';

-- ----------------------------
-- Records of sys_dict_detail
-- ----------------------------
BEGIN;
INSERT INTO `sys_dict_detail` VALUES (1, 1, '激活', 'true', 1, NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict_detail` VALUES (2, 1, '禁用', 'false', 2, NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_detail` VALUES (3, 4, '启用', 'true', 1, NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_detail` VALUES (4, 4, '停用', 'false', 2, NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict_detail` VALUES (5, 5, '启用', 'true', 1, NULL, NULL, NULL, NULL);
INSERT INTO `sys_dict_detail` VALUES (6, 5, '停用', 'false', 2, NULL, NULL, '2019-10-27 20:31:36', NULL);
INSERT INTO `sys_dict_detail` VALUES (10, 7, '是', 'true', 1, 'admin', 'admin', '2024-07-31 15:40:34', '2024-07-31 15:40:34');
INSERT INTO `sys_dict_detail` VALUES (11, 7, '否', 'false', 2, 'admin', 'admin', '2024-07-31 15:40:50', '2024-07-31 15:40:50');
INSERT INTO `sys_dict_detail` VALUES (12, 8, '已发布', 'ACTIVE', 1, 'admin', 'admin', '2024-07-31 15:43:14', '2024-12-10 02:37:42');
INSERT INTO `sys_dict_detail` VALUES (13, 8, '失效', 'DISABLE', 2, 'admin', 'admin', '2024-07-31 15:43:40', '2024-07-31 15:43:40');
INSERT INTO `sys_dict_detail` VALUES (14, 9, '一次', 'once', 1, 'admin', 'admin', '2024-07-31 15:44:51', '2024-07-31 15:44:51');
INSERT INTO `sys_dict_detail` VALUES (19, 10, '进度连续', 'series', 2, 'admin', 'admin', '2024-07-31 15:46:42', '2024-11-18 11:13:03');
INSERT INTO `sys_dict_detail` VALUES (20, 10, '进度累加', 'add', 1, 'admin', 'admin', '2024-07-31 15:46:54', '2024-11-18 11:13:14');
INSERT INTO `sys_dict_detail` VALUES (21, 11, '无奖励', 'none', 1, 'admin', 'admin', '2024-07-31 15:48:07', '2024-07-31 15:48:07');
INSERT INTO `sys_dict_detail` VALUES (22, 11, '固定奖励', 'fixed', 2, 'admin', 'admin', '2024-07-31 15:48:18', '2024-11-18 11:12:24');
INSERT INTO `sys_dict_detail` VALUES (23, 11, '随机奖励', 'fixed_random', 3, 'admin', 'admin', '2024-07-31 15:48:30', '2024-11-18 11:12:08');
INSERT INTO `sys_dict_detail` VALUES (24, 12, '自动发放', 'auto', 1, 'admin', 'admin', '2024-07-31 15:51:55', '2024-07-31 15:51:55');
INSERT INTO `sys_dict_detail` VALUES (25, 12, '提交奖励后领取', 'claim_reward', 2, 'admin', 'admin', '2024-07-31 15:52:15', '2024-07-31 15:52:15');
INSERT INTO `sys_dict_detail` VALUES (26, 12, '提交奖励后等待第三方确认', 'claim_reward_confirm', 3, 'admin', 'admin', '2024-07-31 15:52:27', '2024-07-31 15:52:27');
INSERT INTO `sys_dict_detail` VALUES (27, 12, '任务完成领取', 'claim_task', 4, 'admin', 'admin', '2024-07-31 15:52:40', '2024-07-31 15:52:40');
INSERT INTO `sys_dict_detail` VALUES (28, 13, '不显示', '0', 1, 'admin', 'admin', '2024-07-31 16:04:59', '2024-07-31 16:04:59');
INSERT INTO `sys_dict_detail` VALUES (29, 13, 'go', '1', 2, 'admin', 'admin', '2024-07-31 16:05:13', '2024-07-31 16:05:13');
INSERT INTO `sys_dict_detail` VALUES (30, 13, 'go & verify', '2', 3, 'admin', 'admin', '2024-07-31 16:05:27', '2024-11-18 11:10:03');
INSERT INTO `sys_dict_detail` VALUES (35, 15, '玉米粒', 'MAIZE_KERNEL', 1, 'admin', 'admin', '2024-08-02 17:09:31', '2024-08-02 17:09:31');
INSERT INTO `sys_dict_detail` VALUES (36, 16, 'pc', 'pc', 1, 'admin', 'admin', '2024-08-02 17:10:20', '2024-08-02 17:10:20');
INSERT INTO `sys_dict_detail` VALUES (37, 16, 'app', 'app', 2, 'admin', 'admin', '2024-08-02 17:10:29', '2024-08-02 17:10:29');
INSERT INTO `sys_dict_detail` VALUES (38, 17, 'twitter授权过', 'twitterAuth', 1, 'admin', 'admin', '2024-08-02 17:11:36', '2024-08-02 17:11:36');
INSERT INTO `sys_dict_detail` VALUES (39, 17, 'discord授权过', 'discordAuth', 2, 'admin', 'admin', '2024-08-02 17:12:00', '2024-08-02 17:12:00');
INSERT INTO `sys_dict_detail` VALUES (40, 18, '安卓客户端', 'android', 1, 'admin', 'admin', '2024-08-02 17:12:50', '2024-08-02 17:12:50');
INSERT INTO `sys_dict_detail` VALUES (41, 18, 'ios客户端', 'ios', 2, 'admin', 'admin', '2024-08-02 17:13:01', '2024-08-02 17:13:01');
INSERT INTO `sys_dict_detail` VALUES (42, 13, 'check_in', '3', 4, 'admin', 'admin', '2024-08-05 10:59:53', '2024-11-18 11:10:39');
INSERT INTO `sys_dict_detail` VALUES (44, 19, 'X', 'X', 2, 'admin', 'admin', '2024-08-05 20:59:45', '2024-08-05 20:59:45');
INSERT INTO `sys_dict_detail` VALUES (45, 19, 'Discord', 'Discord', 3, 'admin', 'admin', '2024-08-05 20:59:54', '2024-08-05 20:59:54');
INSERT INTO `sys_dict_detail` VALUES (46, 16, 'app,pc', 'app,pc', 3, 'admin', 'admin', '2024-08-06 17:58:18', '2024-08-06 17:58:18');
INSERT INTO `sys_dict_detail` VALUES (47, 14, 'trex', 'trex', 5, 'admin', 'admin', '2024-11-04 10:51:54', '2024-11-04 10:52:00');
INSERT INTO `sys_dict_detail` VALUES (49, 20, 'invite_register', 'invite_register', 999, 'admin', 'admin', '2024-11-15 17:25:00', '2024-11-15 17:25:00');
INSERT INTO `sys_dict_detail` VALUES (50, 20, 'connect_x', 'connect_x', 999, 'admin', 'admin', '2024-11-15 17:25:00', '2024-11-15 17:25:00');
INSERT INTO `sys_dict_detail` VALUES (51, 20, 'connect_discord', 'connect_discord', 999, 'admin', 'admin', '2024-11-15 17:29:24', '2024-11-15 17:29:24');
INSERT INTO `sys_dict_detail` VALUES (52, 20, 'connect_google', 'connect_google', 999, 'admin', 'admin', '2024-11-15 17:46:06', '2024-11-15 17:46:06');
INSERT INTO `sys_dict_detail` VALUES (53, 20, 'connect_tiktok', 'connect_tiktok', 1, 'admin', 'admin', '2024-11-15 17:51:26', '2024-11-15 17:51:26');
INSERT INTO `sys_dict_detail` VALUES (55, 20, 'connect_instagram', 'connect_instagram', 1, 'admin', 'admin', '2024-11-15 20:55:09', '2024-11-15 20:55:09');
INSERT INTO `sys_dict_detail` VALUES (56, 19, 'Google', 'Google', 4, 'admin', 'admin', '2024-11-15 20:57:03', '2024-11-15 20:57:03');
INSERT INTO `sys_dict_detail` VALUES (58, 21, 'EOA_ASSET', 'EOA_ASSET', 1, 'admin', 'admin', '2024-11-18 16:13:09', '2024-11-18 16:13:09');
INSERT INTO `sys_dict_detail` VALUES (60, 8, '灰度中', 'GRAY', 3, 'admin', 'admin', '2024-11-21 01:15:01', '2024-11-21 01:15:01');
INSERT INTO `sys_dict_detail` VALUES (61, 23, 'true', '1', 0, 'admin', 'admin', '2024-11-23 09:37:27', '2024-11-23 09:37:27');
INSERT INTO `sys_dict_detail` VALUES (62, 23, 'false', '0', 1, 'admin', 'admin', '2024-11-23 09:37:37', '2024-11-23 09:37:37');
INSERT INTO `sys_dict_detail` VALUES (68, 19, 'TikTok', 'TikTok', 5, 'admin', 'admin', '2024-12-04 02:28:55', '2024-12-04 02:28:55');
INSERT INTO `sys_dict_detail` VALUES (69, 19, 'Instagram', 'Instagram', 6, 'admin', 'admin', '2024-12-04 02:29:22', '2024-12-04 02:29:22');
INSERT INTO `sys_dict_detail` VALUES (89, 21, 'COMMUNITY_TO_SPACE', 'COMMUNITY_TO_SPACE', 2, 'admin', 'admin', '2024-12-12 06:38:10', '2024-12-12 06:38:10');
INSERT INTO `sys_dict_detail` VALUES (92, 19, 'YouTube', 'YouTube', 7, 'admin', 'admin', '2024-12-12 08:50:22', '2024-12-12 08:50:22');
INSERT INTO `sys_dict_detail` VALUES (148, 24, 'SLG 内测抽奖活动', 'SLG_innertest_lottery', 1, NULL, NULL, '2025-04-02 15:04:41', '2025-04-02 15:04:41');
INSERT INTO `sys_dict_detail` VALUES (149, 25, 'SLG 内测抽奖活动周期 - 第一周', 'SLG_innertest_lottery_weekly_001', 1, NULL, NULL, '2025-04-02 15:06:36', '2025-04-02 15:06:50');
INSERT INTO `sys_dict_detail` VALUES (150, 25, 'SLG 内测抽奖活动周期 - 第二周', 'SLG_innertest_lottery_weekly_002', 2, NULL, NULL, '2025-04-02 15:07:21', '2025-04-02 15:07:21');
INSERT INTO `sys_dict_detail` VALUES (151, 25, 'SLG 内测抽奖活动周期 - 第三周', 'SLG_innertest_lottery_weekly_003', 3, NULL, NULL, '2025-04-02 15:07:35', '2025-04-02 15:07:35');
INSERT INTO `sys_dict_detail` VALUES (152, 25, 'SLG 内测抽奖活动周期 - 第四周', 'SLG_innertest_lottery_weekly_004', 4, NULL, NULL, '2025-04-02 15:07:47', '2025-04-02 15:07:47');
INSERT INTO `sys_dict_detail` VALUES (160, 26, 'podcast', 'podcast', 1, NULL, NULL, '2025-05-06 09:37:28', '2025-05-06 09:37:28');
INSERT INTO `sys_dict_detail` VALUES (161, 26, 'dapp', 'dapp', 2, NULL, NULL, '2025-05-06 09:37:37', '2025-05-06 09:37:37');
INSERT INTO `sys_dict_detail` VALUES (162, 27, 'GameFi', 'GameFi', 1, NULL, NULL, '2025-05-06 09:41:58', '2025-05-06 09:41:58');
INSERT INTO `sys_dict_detail` VALUES (163, 27, 'SocialFi', 'SocialFi', 2, NULL, NULL, '2025-05-06 09:42:06', '2025-05-06 09:42:06');
INSERT INTO `sys_dict_detail` VALUES (164, 27, 'DeFi', 'DeFi', 3, NULL, NULL, '2025-05-06 09:42:33', '2025-05-06 09:42:33');
INSERT INTO `sys_dict_detail` VALUES (165, 28, 'drex-core', 'drex-core', 1, NULL, NULL, '2025-05-07 09:06:53', '2025-05-07 09:06:53');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(166, 20, 'invite_register', 'invite_register', 999, null, null, '2025-05-16 08:20:51', '2025-05-16 08:20:51');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(167, 29, 'Pending', 'Pending', 1, null, null, '2025-05-19 06:00:51', '2025-05-19 06:00:51');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(168, 29, 'Completed', 'Completed', 2, null, null, '2025-05-19 06:01:00', '2025-05-19 06:01:00');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(169, 29, 'Deleted', 'Deleted', 3, null, null, '2025-05-19 06:01:10', '2025-05-19 06:01:10');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(170, 30, '所有用户', '1', 1, null, null, '2025-05-19 06:01:49', '2025-05-19 06:01:49');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(171, 30, '内测用户', '2', 2, null, null, '2025-05-19 06:01:57', '2025-05-19 06:01:57');
INSERT IGNORE INTO `eladmin`.`sys_dict_detail` (`detail_id`, `dict_id`, `label`, `value`, `dict_sort`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(172, 31, 'trex', 'trex', 999, null, null, '2025-05-20 08:54:48', '2025-05-20 08:54:48');
COMMIT;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位名称',
  `enabled` bit(1) NOT NULL COMMENT '岗位状态',
  `job_sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`job_id`) USING BTREE,
  UNIQUE KEY `uniq_name` (`name`),
  KEY `inx_enabled` (`enabled`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='岗位';

-- ----------------------------
-- Records of sys_job
-- ----------------------------
BEGIN;
INSERT INTO `sys_job` VALUES (8, '人事专员', b'1', 3, NULL, NULL, '2019-03-29 14:52:28', NULL);
INSERT INTO `sys_job` VALUES (10, '产品经理', b'1', 4, NULL, NULL, '2019-03-29 14:55:51', NULL);
INSERT INTO `sys_job` VALUES (11, '全栈开发', b'1', 2, NULL, 'admin', '2019-03-31 13:39:30', '2020-05-05 11:33:43');
INSERT INTO `sys_job` VALUES (12, '软件测试', b'1', 5, NULL, 'admin', '2019-03-31 13:39:43', '2020-05-10 19:56:26');
COMMIT;

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `log_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `request_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `time` bigint DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `browser` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `exception_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `log_create_time_index` (`create_time`),
  KEY `inx_log_type` (`log_type`)
) ENGINE=InnoDB AUTO_INCREMENT=10816 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='系统日志';

-- ----------------------------
-- Records of sys_log
-- ----------------------------


-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` bigint DEFAULT NULL COMMENT '上级菜单ID',
  `sub_count` int DEFAULT '0' COMMENT '子菜单数目',
  `type` int DEFAULT NULL COMMENT '菜单类型',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单标题',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件名称',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件',
  `menu_sort` int DEFAULT NULL COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接地址',
  `i_frame` bit(1) DEFAULT NULL COMMENT '是否外链',
  `cache` bit(1) DEFAULT b'0' COMMENT '缓存',
  `hidden` bit(1) DEFAULT b'0' COMMENT '隐藏',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`menu_id`) USING BTREE,
  UNIQUE KEY `uniq_title` (`title`),
  UNIQUE KEY `uniq_name` (`name`),
  KEY `inx_pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=131 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='系统菜单';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
BEGIN;
INSERT INTO `sys_menu` VALUES (1, NULL, 7, 0, '系统管理', NULL, NULL, 1, 'system', 'system', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-18 15:11:29', NULL);
INSERT INTO `sys_menu` VALUES (2, 1, 3, 1, '用户管理', 'User', 'system/user/index', 2, 'peoples', 'user', b'0', b'0', b'0', 'user:list', NULL, NULL, '2018-12-18 15:14:44', NULL);
INSERT INTO `sys_menu` VALUES (3, 1, 3, 1, '角色管理', 'Role', 'system/role/index', 3, 'role', 'role', b'0', b'0', b'0', 'roles:list', NULL, NULL, '2018-12-18 15:16:07', NULL);
INSERT INTO `sys_menu` VALUES (5, 1, 3, 1, '菜单管理', 'Menu', 'system/menu/index', 5, 'menu', 'menu', b'0', b'0', b'0', 'menu:list', NULL, NULL, '2018-12-18 15:17:28', NULL);
INSERT INTO `sys_menu` VALUES (6, NULL, 5, 0, '系统监控', NULL, NULL, 10, 'monitor', 'monitor', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-18 15:17:48', NULL);
INSERT INTO `sys_menu` VALUES (7, 6, 0, 1, '操作日志', 'Log', 'monitor/log/index', 11, 'log', 'logs', b'0', b'1', b'0', NULL, NULL, 'admin', '2018-12-18 15:18:26', '2020-06-06 13:11:57');
INSERT INTO `sys_menu` VALUES (9, 6, 0, 1, 'SQL监控', 'Sql', 'monitor/sql/index', 18, 'sqlMonitor', 'druid', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-18 15:19:34', NULL);
INSERT INTO `sys_menu` VALUES (10, NULL, 5, 0, '组件管理', NULL, NULL, 50, 'zujian', 'components', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-19 13:38:16', NULL);
INSERT INTO `sys_menu` VALUES (11, 10, 0, 1, '图标库', 'Icons', 'components/icons/index', 51, 'icon', 'icon', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-19 13:38:49', NULL);
INSERT INTO `sys_menu` VALUES (14, 36, 0, 1, '邮件工具', 'Email', 'tools/email/index', 35, 'email', 'email', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-27 10:13:09', NULL);
INSERT INTO `sys_menu` VALUES (15, 10, 0, 1, '富文本', 'Editor', 'components/Editor', 52, 'fwb', 'tinymce', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-27 11:58:25', NULL);
INSERT INTO `sys_menu` VALUES (18, 36, 3, 1, '存储管理', 'Storage', 'tools/storage/index', 34, 'qiniu', 'storage', b'0', b'0', b'0', 'storage:list', NULL, NULL, '2018-12-31 11:12:15', NULL);
INSERT INTO `sys_menu` VALUES (19, 36, 0, 1, '支付宝工具', 'AliPay', 'tools/aliPay/index', 37, 'alipay', 'aliPay', b'0', b'0', b'0', NULL, NULL, NULL, '2018-12-31 14:52:38', NULL);
INSERT INTO `sys_menu` VALUES (21, NULL, 7, 0, '任务平台', NULL, '', 0, 'menu', 'quests', b'0', b'0', b'0', NULL, NULL, 'admin', '2019-01-04 16:22:03', '2024-09-29 12:15:46');
INSERT INTO `sys_menu` VALUES (23, 21, 1, 1, '任务配置', NULL, 'quests/task/config/index', 1, 'nested', 'task/config', b'0', b'0', b'0', NULL, NULL, 'admin', '2019-01-04 16:23:57', '2024-10-18 17:43:51');
INSERT INTO `sys_menu` VALUES (28, 1, 3, 1, '任务调度', 'Timing', 'system/timing/index', 999, 'timing', 'timing', b'0', b'0', b'0', 'timing:list', NULL, NULL, '2019-01-07 20:34:40', NULL);
INSERT INTO `sys_menu` VALUES (30, 36, 0, 1, '代码生成', 'GeneratorIndex', 'generator/index', 32, 'dev', 'generator', b'0', b'1', b'0', NULL, NULL, NULL, '2019-01-11 15:45:55', NULL);
INSERT INTO `sys_menu` VALUES (32, 6, 0, 1, '异常日志', 'ErrorLog', 'monitor/log/errorLog', 12, 'error', 'errorLog', b'0', b'0', b'0', NULL, NULL, NULL, '2019-01-13 13:49:03', NULL);
INSERT INTO `sys_menu` VALUES (33, 10, 0, 1, 'Markdown', 'Markdown', 'components/MarkDown', 53, 'markdown', 'markdown', b'0', b'0', b'0', NULL, NULL, NULL, '2019-03-08 13:46:44', NULL);
INSERT INTO `sys_menu` VALUES (34, 10, 0, 1, 'Yaml编辑器', 'YamlEdit', 'components/YamlEdit', 54, 'dev', 'yaml', b'0', b'0', b'0', NULL, NULL, NULL, '2019-03-08 15:49:40', NULL);
INSERT INTO `sys_menu` VALUES (35, 1, 3, 1, '部门管理', 'Dept', 'system/dept/index', 6, 'dept', 'dept', b'0', b'0', b'0', 'dept:list', NULL, NULL, '2019-03-25 09:46:00', NULL);
INSERT INTO `sys_menu` VALUES (36, NULL, 7, 0, '系统工具', NULL, '', 30, 'sys-tools', 'sys-tools', b'0', b'0', b'0', NULL, NULL, NULL, '2019-03-29 10:57:35', NULL);
INSERT INTO `sys_menu` VALUES (37, 1, 3, 1, '岗位管理', 'Job', 'system/job/index', 7, 'Steve-Jobs', 'job', b'0', b'0', b'0', 'job:list', NULL, NULL, '2019-03-29 13:51:18', NULL);
INSERT INTO `sys_menu` VALUES (38, 36, 0, 1, '接口文档', 'Swagger', 'tools/swagger/index', 36, 'swagger', 'swagger2', b'0', b'0', b'0', NULL, NULL, NULL, '2019-03-29 19:57:53', NULL);
INSERT INTO `sys_menu` VALUES (39, 1, 3, 1, '字典管理', 'Dict', 'system/dict/index', 8, 'dictionary', 'dict', b'0', b'0', b'0', 'dict:list', NULL, NULL, '2019-04-10 11:49:04', NULL);
INSERT INTO `sys_menu` VALUES (41, 6, 0, 1, '在线用户', 'OnlineUser', 'monitor/online/index', 10, 'Steve-Jobs', 'online', b'0', b'0', b'0', NULL, NULL, NULL, '2019-10-26 22:08:43', NULL);
INSERT INTO `sys_menu` VALUES (44, 2, 0, 2, '用户新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'user:add', NULL, NULL, '2019-10-29 10:59:46', NULL);
INSERT INTO `sys_menu` VALUES (45, 2, 0, 2, '用户编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'user:edit', NULL, NULL, '2019-10-29 11:00:08', NULL);
INSERT INTO `sys_menu` VALUES (46, 2, 0, 2, '用户删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'user:del', NULL, NULL, '2019-10-29 11:00:23', NULL);
INSERT INTO `sys_menu` VALUES (48, 3, 0, 2, '角色创建', NULL, '', 2, '', '', b'0', b'0', b'0', 'roles:add', NULL, NULL, '2019-10-29 12:45:34', NULL);
INSERT INTO `sys_menu` VALUES (49, 3, 0, 2, '角色修改', NULL, '', 3, '', '', b'0', b'0', b'0', 'roles:edit', NULL, NULL, '2019-10-29 12:46:16', NULL);
INSERT INTO `sys_menu` VALUES (50, 3, 0, 2, '角色删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'roles:del', NULL, NULL, '2019-10-29 12:46:51', NULL);
INSERT INTO `sys_menu` VALUES (52, 5, 0, 2, '菜单新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'menu:add', NULL, NULL, '2019-10-29 12:55:07', NULL);
INSERT INTO `sys_menu` VALUES (53, 5, 0, 2, '菜单编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'menu:edit', NULL, NULL, '2019-10-29 12:55:40', NULL);
INSERT INTO `sys_menu` VALUES (54, 5, 0, 2, '菜单删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'menu:del', NULL, NULL, '2019-10-29 12:56:00', NULL);
INSERT INTO `sys_menu` VALUES (56, 35, 0, 2, '部门新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'dept:add', NULL, NULL, '2019-10-29 12:57:09', NULL);
INSERT INTO `sys_menu` VALUES (57, 35, 0, 2, '部门编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'dept:edit', NULL, NULL, '2019-10-29 12:57:27', NULL);
INSERT INTO `sys_menu` VALUES (58, 35, 0, 2, '部门删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'dept:del', NULL, NULL, '2019-10-29 12:57:41', NULL);
INSERT INTO `sys_menu` VALUES (60, 37, 0, 2, '岗位新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'job:add', NULL, NULL, '2019-10-29 12:58:27', NULL);
INSERT INTO `sys_menu` VALUES (61, 37, 0, 2, '岗位编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'job:edit', NULL, NULL, '2019-10-29 12:58:45', NULL);
INSERT INTO `sys_menu` VALUES (62, 37, 0, 2, '岗位删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'job:del', NULL, NULL, '2019-10-29 12:59:04', NULL);
INSERT INTO `sys_menu` VALUES (64, 39, 0, 2, '字典新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'dict:add', NULL, NULL, '2019-10-29 13:00:17', NULL);
INSERT INTO `sys_menu` VALUES (65, 39, 0, 2, '字典编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'dict:edit', NULL, NULL, '2019-10-29 13:00:42', NULL);
INSERT INTO `sys_menu` VALUES (66, 39, 0, 2, '字典删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'dict:del', NULL, NULL, '2019-10-29 13:00:59', NULL);
INSERT INTO `sys_menu` VALUES (73, 28, 0, 2, '任务新增', NULL, '', 2, '', '', b'0', b'0', b'0', 'timing:add', NULL, NULL, '2019-10-29 13:07:28', NULL);
INSERT INTO `sys_menu` VALUES (74, 28, 0, 2, '任务编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'timing:edit', NULL, NULL, '2019-10-29 13:07:41', NULL);
INSERT INTO `sys_menu` VALUES (75, 28, 0, 2, '任务删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'timing:del', NULL, NULL, '2019-10-29 13:07:54', NULL);
INSERT INTO `sys_menu` VALUES (77, 18, 0, 2, '上传文件', NULL, '', 2, '', '', b'0', b'0', b'0', 'storage:add', NULL, NULL, '2019-10-29 13:09:09', NULL);
INSERT INTO `sys_menu` VALUES (78, 18, 0, 2, '文件编辑', NULL, '', 3, '', '', b'0', b'0', b'0', 'storage:edit', NULL, NULL, '2019-10-29 13:09:22', NULL);
INSERT INTO `sys_menu` VALUES (79, 18, 0, 2, '文件删除', NULL, '', 4, '', '', b'0', b'0', b'0', 'storage:del', NULL, NULL, '2019-10-29 13:09:34', NULL);
INSERT INTO `sys_menu` VALUES (80, 6, 0, 1, '服务监控', 'ServerMonitor', 'monitor/server/index', 14, 'codeConsole', 'server', b'0', b'0', b'0', 'monitor:list', NULL, 'admin', '2019-11-07 13:06:39', '2020-05-04 18:20:50');
INSERT INTO `sys_menu` VALUES (82, 36, 0, 1, '生成配置', 'GeneratorConfig', 'generator/config', 33, 'dev', 'generator/config/:tableName', b'0', b'1', b'1', '', NULL, NULL, '2019-11-17 20:08:56', NULL);
INSERT INTO `sys_menu` VALUES (83, 10, 0, 1, '图表库', 'Echarts', 'components/Echarts', 50, 'chart', 'echarts', b'0', b'1', b'0', '', NULL, NULL, '2019-11-21 09:04:32', NULL);
INSERT INTO `sys_menu` VALUES (90, NULL, 5, 1, '运维管理', 'Mnt', '', 20, 'mnt', 'mnt', b'0', b'0', b'0', NULL, NULL, NULL, '2019-11-09 10:31:08', NULL);
INSERT INTO `sys_menu` VALUES (92, 90, 3, 1, '服务器', 'ServerDeploy', 'mnt/server/index', 22, 'server', 'mnt/serverDeploy', b'0', b'0', b'0', 'serverDeploy:list', NULL, NULL, '2019-11-10 10:29:25', NULL);
INSERT INTO `sys_menu` VALUES (93, 90, 3, 1, '应用管理', 'App', 'mnt/app/index', 23, 'app', 'mnt/app', b'0', b'0', b'0', 'app:list', NULL, NULL, '2019-11-10 11:05:16', NULL);
INSERT INTO `sys_menu` VALUES (94, 90, 3, 1, '部署管理', 'Deploy', 'mnt/deploy/index', 24, 'deploy', 'mnt/deploy', b'0', b'0', b'0', 'deploy:list', NULL, NULL, '2019-11-10 15:56:55', NULL);
INSERT INTO `sys_menu` VALUES (97, 90, 1, 1, '部署备份', 'DeployHistory', 'mnt/deployHistory/index', 25, 'backup', 'mnt/deployHistory', b'0', b'0', b'0', 'deployHistory:list', NULL, NULL, '2019-11-10 16:49:44', NULL);
INSERT INTO `sys_menu` VALUES (98, 90, 3, 1, '数据库管理', 'Database', 'mnt/database/index', 26, 'database', 'mnt/database', b'0', b'0', b'0', 'database:list', NULL, NULL, '2019-11-10 20:40:04', NULL);
INSERT INTO `sys_menu` VALUES (102, 97, 0, 2, '删除', NULL, '', 999, '', '', b'0', b'0', b'0', 'deployHistory:del', NULL, NULL, '2019-11-17 09:32:48', NULL);
INSERT INTO `sys_menu` VALUES (103, 92, 0, 2, '服务器新增', NULL, '', 999, '', '', b'0', b'0', b'0', 'serverDeploy:add', NULL, NULL, '2019-11-17 11:08:33', NULL);
INSERT INTO `sys_menu` VALUES (104, 92, 0, 2, '服务器编辑', NULL, '', 999, '', '', b'0', b'0', b'0', 'serverDeploy:edit', NULL, NULL, '2019-11-17 11:08:57', NULL);
INSERT INTO `sys_menu` VALUES (105, 92, 0, 2, '服务器删除', NULL, '', 999, '', '', b'0', b'0', b'0', 'serverDeploy:del', NULL, NULL, '2019-11-17 11:09:15', NULL);
INSERT INTO `sys_menu` VALUES (106, 93, 0, 2, '应用新增', NULL, '', 999, '', '', b'0', b'0', b'0', 'app:add', NULL, NULL, '2019-11-17 11:10:03', NULL);
INSERT INTO `sys_menu` VALUES (107, 93, 0, 2, '应用编辑', NULL, '', 999, '', '', b'0', b'0', b'0', 'app:edit', NULL, NULL, '2019-11-17 11:10:28', NULL);
INSERT INTO `sys_menu` VALUES (108, 93, 0, 2, '应用删除', NULL, '', 999, '', '', b'0', b'0', b'0', 'app:del', NULL, NULL, '2019-11-17 11:10:55', NULL);
INSERT INTO `sys_menu` VALUES (109, 94, 0, 2, '部署新增', NULL, '', 999, '', '', b'0', b'0', b'0', 'deploy:add', NULL, NULL, '2019-11-17 11:11:22', NULL);
INSERT INTO `sys_menu` VALUES (110, 94, 0, 2, '部署编辑', NULL, '', 999, '', '', b'0', b'0', b'0', 'deploy:edit', NULL, NULL, '2019-11-17 11:11:41', NULL);
INSERT INTO `sys_menu` VALUES (111, 94, 0, 2, '部署删除', NULL, '', 999, '', '', b'0', b'0', b'0', 'deploy:del', NULL, NULL, '2019-11-17 11:12:01', NULL);
INSERT INTO `sys_menu` VALUES (112, 98, 0, 2, '数据库新增', NULL, '', 999, '', '', b'0', b'0', b'0', 'database:add', NULL, NULL, '2019-11-17 11:12:43', NULL);
INSERT INTO `sys_menu` VALUES (113, 98, 0, 2, '数据库编辑', NULL, '', 999, '', '', b'0', b'0', b'0', 'database:edit', NULL, NULL, '2019-11-17 11:12:58', NULL);
INSERT INTO `sys_menu` VALUES (114, 98, 0, 2, '数据库删除', NULL, '', 999, '', '', b'0', b'0', b'0', 'database:del', NULL, NULL, '2019-11-17 11:13:14', NULL);
INSERT INTO `sys_menu` VALUES (116, 36, 0, 1, '生成预览', 'Preview', 'generator/preview', 999, 'java', 'generator/preview/:tableName', b'0', b'1', b'1', NULL, NULL, NULL, '2019-11-26 14:54:36', NULL);
INSERT INTO `sys_menu` VALUES (119, 23, 0, 2, '上传icon', NULL, NULL, 999, NULL, NULL, b'0', b'0', b'0', NULL, 'admin', 'admin', '2024-09-19 17:03:58', '2024-09-19 17:03:58');
INSERT INTO `sys_menu` VALUES (120, 21, 0, 1, '任务事件', 'taskCode', 'quests/task/code/index', 2, 'server', 'task/code', b'0', b'0', b'0', NULL, 'admin', 'admin', '2024-09-26 16:31:33', '2024-09-30 02:26:54');
INSERT INTO `sys_menu` VALUES (121, 21, 1, 1, '任务icon', NULL, 'quests/task/icon/index', 999, 'icon', 'task/icon', b'0', b'0', b'0', 'taskIcon:list', 'admin', 'admin', '2024-09-27 11:42:51', '2024-09-29 12:17:06');
INSERT INTO `sys_menu` VALUES (122, 121, 0, 2, '上传任务icon', NULL, NULL, 999, NULL, NULL, b'0', b'0', b'0', 'taskIcon:upload', 'admin', 'admin', '2024-09-27 16:27:11', '2024-09-27 16:27:11');
INSERT INTO `sys_menu` VALUES (123, 21, 0, 1, '灰度白名单', 'TaskWhitelist', 'quests/task/whitelist/index', 999, 'people', 'task/whitelist', b'0', b'0', b'0', NULL, 'admin', 'admin', '2024-11-11 15:05:49', '2024-11-11 16:09:14');
INSERT INTO `sys_menu` VALUES (124, 21, 1, 1, '用户查询', '用户查询', 'quests/task/user/index', 0, 'Steve-Jobs', 'task/user', b'0', b'0', b'0', 'taskUser:list', 'admin', 'admin', '2024-12-13 16:58:21', '2025-01-20 10:15:37');
INSERT INTO `sys_menu` VALUES (125, 124, 0, 2, '搜索', NULL, NULL, 1, NULL, NULL, b'0', b'0', b'0', 'taskUser:list', 'admin', 'admin', '2025-01-20 10:16:17', '2025-01-20 10:16:17');
INSERT INTO `sys_menu` VALUES (128, NULL, 2, 0, 'D-Rex', NULL, NULL, 0, 'doc', 'drex', b'0', b'0', b'0', NULL, NULL, NULL, '2025-05-06 10:06:21', '2025-05-06 10:09:51');
INSERT INTO `sys_menu` VALUES (129, 128, 0, 1, '项目信息配置', NULL, 'drex/information/index', 1, 'app', 'information', b'0', b'0', b'0', NULL, NULL, NULL, '2025-05-06 10:09:04', '2025-05-06 10:09:04');
INSERT INTO `sys_menu` VALUES (130, 128, 0, 1, '运营参数配置', NULL, 'drex/config/index', 2, 'edit', 'config', b'0', b'0', b'0', NULL, NULL, NULL, '2025-05-07 09:01:08', '2025-05-07 09:01:08');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(131, 128, 4, 1, '通知消息配置', null, 'drex/notice/index', 3, 'dev', 'notice', 0, 0, 0, null, null, null, '2025-05-14 08:33:45', '2025-05-14 08:33:45');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(132, 129, 0, 2, '新增项目', null, null, 1, null, null, 0, 0, 0, 'information:add', null, null, '2025-05-21 08:52:21', '2025-05-21 08:56:23');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(133, 129, 0, 2, '编辑项目', null, null, 2, null, null, 0, 0, 0, 'information:edit', null, null, '2025-05-21 08:52:48', '2025-05-21 08:56:31');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(134, 129, 0, 2, '删除项目', null, null, 3, null, null, 0, 0, 0, 'information:del', null, null, '2025-05-21 08:53:15', '2025-05-21 08:56:41');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(135, 129, 0, 2, '项目列表', null, null, 4, null, null, 0, 0, 0, 'information:list', null, null, '2025-05-21 08:53:38', '2025-05-21 08:56:49');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(136, 131, 0, 2, '通知列表', null, null, 4, null, null, 0, 0, 0, 'notice:list', null, null, '2025-05-21 08:59:16', '2025-05-21 09:04:53');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(137, 131, 0, 2, '新增通知', null, null, 1, null, null, 0, 0, 0, 'notice:add', null, null, '2025-05-21 09:03:45', '2025-05-21 09:03:45');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(138, 131, 0, 2, '编辑通知', null, null, 2, null, null, 0, 0, 0, 'notice:edit', null, null, '2025-05-21 09:04:12', '2025-05-21 09:04:12');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(139, 131, 0, 2, '删除通知', null, null, 3, null, null, 0, 0, 0, 'notice:del', null, null, '2025-05-21 09:04:34', '2025-05-21 09:04:34');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(140, 128, 3, 1, '恐龙配置', null, 'drex/rexy/index', 4, 'develop', 'rexy/index', 0, 0, 0, 'rexyConfig:list', null, null, '2025-05-23 06:56:50', '2025-05-23 06:56:50');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(141, 140, 0, 2, 'add', null, null, 1, null, null, 0, 0, 0, 'rexyConfig:add', null, null, '2025-05-23 06:58:00', '2025-05-23 06:58:00');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(142, 140, 0, 2, 'edit', null, null, 2, null, null, 0, 0, 0, 'rexyConfig:edit', null, null, '2025-05-23 06:58:40', '2025-05-23 06:58:40');
INSERT IGNORE INTO `eladmin`.`sys_menu` (`menu_id`, `pid`, `sub_count`, `type`, `title`, `name`, `component`, `menu_sort`, `icon`, `path`, `i_frame`, `cache`, `hidden`, `permission`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(143, 140, 0, 2, 'del', null, null, 3, null, null, 0, 0, 0, 'rexyConfig:del', null, null, '2025-05-23 06:59:16', '2025-05-23 06:59:16');

COMMIT;

-- ----------------------------
-- Table structure for sys_quartz_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_quartz_job`;
CREATE TABLE `sys_quartz_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bean_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Spring Bean名称',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'cron 表达式',
  `is_pause` bit(1) DEFAULT NULL COMMENT '状态：1暂停、0启用',
  `job_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务名称',
  `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '方法名称',
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '参数',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `person_in_charge` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '负责人',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '报警邮箱',
  `sub_task` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '子任务ID',
  `pause_after_failure` bit(1) DEFAULT NULL COMMENT '任务失败后是否暂停',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`job_id`) USING BTREE,
  KEY `inx_is_pause` (`is_pause`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='定时任务';

-- ----------------------------
-- Records of sys_quartz_job
-- ----------------------------
BEGIN;
INSERT INTO `sys_quartz_job` VALUES (2, 'testTask', '0/5 * * * * ?', b'1', '测试1', 'run1', 'test', '带参测试，多参使用json', '测试', NULL, NULL, NULL, NULL, 'admin', '2019-08-22 14:08:29', '2020-05-24 13:58:33');
INSERT INTO `sys_quartz_job` VALUES (3, 'testTask', '0/5 * * * * ?', b'1', '测试', 'run', '', '不带参测试', 'Zheng Jie', '', '5,6', b'1', NULL, 'admin', '2019-09-26 16:44:39', '2020-05-24 14:48:12');
INSERT INTO `sys_quartz_job` VALUES (5, 'Test', '0/5 * * * * ?', b'1', '任务告警测试', 'run', NULL, '测试', 'test', '', NULL, b'1', 'admin', 'admin', '2020-05-05 20:32:41', '2020-05-05 20:36:13');
INSERT INTO `sys_quartz_job` VALUES (6, 'testTask', '0/5 * * * * ?', b'1', '测试3', 'run2', NULL, '测试3', 'Zheng Jie', '', NULL, b'1', 'admin', 'admin', '2020-05-05 20:35:41', '2020-05-05 20:36:07');
INSERT INTO `sys_quartz_job` VALUES (7, 'taskConfigPublish', '0 * * * * ?', b'0', 'task_publish', 'publish', NULL, 'task_publish', 'li', NULL, NULL, b'0', 'admin', 'admin', '2024-12-01 14:37:43', '2024-12-01 14:37:43');
COMMIT;

-- ----------------------------
-- Table structure for sys_quartz_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_quartz_log`;
CREATE TABLE `sys_quartz_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bean_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `exception_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `is_success` bit(1) DEFAULT NULL,
  `job_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `time` bigint DEFAULT NULL,
  PRIMARY KEY (`log_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=271794 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='定时任务日志';

-- ----------------------------
-- Records of sys_quartz_log
-- ----------------------------


-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `level` int DEFAULT NULL COMMENT '角色级别',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `data_scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据权限',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`role_id`) USING BTREE,
  UNIQUE KEY `uniq_name` (`name`),
  KEY `role_name_index` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='角色表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
BEGIN;
INSERT INTO `sys_role` VALUES (1, '超级管理员', 1, '-', '全部', NULL, 'admin', '2018-11-23 11:04:37', '2025-05-07 09:01:18');
INSERT INTO `sys_role` VALUES (2, '普通用户', 2, '-', '本级', NULL, 'admin', '2018-11-23 13:09:06', '2020-09-05 10:45:12');
INSERT IGNORE INTO `eladmin`.`sys_role` (`role_id`, `name`, `level`, `description`, `data_scope`, `create_by`, `update_by`, `create_time`, `update_time`) VALUES
(3, '运营管理', 2, '运营管理', '本级', null, null, '2025-05-21 08:46:22', '2025-05-23 06:59:48');
COMMIT;

-- ----------------------------
-- Table structure for sys_roles_depts
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles_depts`;
CREATE TABLE `sys_roles_depts` (
  `role_id` bigint NOT NULL,
  `dept_id` bigint NOT NULL,
  PRIMARY KEY (`role_id`,`dept_id`) USING BTREE,
  KEY `FK7qg6itn5ajdoa9h9o78v9ksur` (`dept_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='角色部门关联';

-- ----------------------------
-- Records of sys_roles_depts
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for sys_roles_menus
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles_menus`;
CREATE TABLE `sys_roles_menus` (
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`menu_id`,`role_id`) USING BTREE,
  KEY `FKcngg2qadojhi3a651a5adkvbq` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='角色菜单关联';

-- ----------------------------
-- Records of sys_roles_menus
-- ----------------------------
BEGIN;
INSERT INTO `sys_roles_menus` VALUES (1, 1);
INSERT INTO `sys_roles_menus` VALUES (2, 1);
INSERT INTO `sys_roles_menus` VALUES (3, 1);
INSERT INTO `sys_roles_menus` VALUES (5, 1);
INSERT INTO `sys_roles_menus` VALUES (6, 1);
INSERT INTO `sys_roles_menus` VALUES (7, 1);
INSERT INTO `sys_roles_menus` VALUES (9, 1);
INSERT INTO `sys_roles_menus` VALUES (10, 1);
INSERT INTO `sys_roles_menus` VALUES (11, 1);
INSERT INTO `sys_roles_menus` VALUES (14, 1);
INSERT INTO `sys_roles_menus` VALUES (15, 1);
INSERT INTO `sys_roles_menus` VALUES (18, 1);
INSERT INTO `sys_roles_menus` VALUES (19, 1);
INSERT INTO `sys_roles_menus` VALUES (21, 1);
INSERT INTO `sys_roles_menus` VALUES (23, 1);
INSERT INTO `sys_roles_menus` VALUES (28, 1);
INSERT INTO `sys_roles_menus` VALUES (30, 1);
INSERT INTO `sys_roles_menus` VALUES (32, 1);
INSERT INTO `sys_roles_menus` VALUES (33, 1);
INSERT INTO `sys_roles_menus` VALUES (34, 1);
INSERT INTO `sys_roles_menus` VALUES (35, 1);
INSERT INTO `sys_roles_menus` VALUES (36, 1);
INSERT INTO `sys_roles_menus` VALUES (37, 1);
INSERT INTO `sys_roles_menus` VALUES (38, 1);
INSERT INTO `sys_roles_menus` VALUES (39, 1);
INSERT INTO `sys_roles_menus` VALUES (41, 1);
INSERT INTO `sys_roles_menus` VALUES (44, 1);
INSERT INTO `sys_roles_menus` VALUES (45, 1);
INSERT INTO `sys_roles_menus` VALUES (46, 1);
INSERT INTO `sys_roles_menus` VALUES (48, 1);
INSERT INTO `sys_roles_menus` VALUES (49, 1);
INSERT INTO `sys_roles_menus` VALUES (50, 1);
INSERT INTO `sys_roles_menus` VALUES (52, 1);
INSERT INTO `sys_roles_menus` VALUES (53, 1);
INSERT INTO `sys_roles_menus` VALUES (54, 1);
INSERT INTO `sys_roles_menus` VALUES (56, 1);
INSERT INTO `sys_roles_menus` VALUES (57, 1);
INSERT INTO `sys_roles_menus` VALUES (58, 1);
INSERT INTO `sys_roles_menus` VALUES (60, 1);
INSERT INTO `sys_roles_menus` VALUES (61, 1);
INSERT INTO `sys_roles_menus` VALUES (62, 1);
INSERT INTO `sys_roles_menus` VALUES (64, 1);
INSERT INTO `sys_roles_menus` VALUES (65, 1);
INSERT INTO `sys_roles_menus` VALUES (66, 1);
INSERT INTO `sys_roles_menus` VALUES (73, 1);
INSERT INTO `sys_roles_menus` VALUES (74, 1);
INSERT INTO `sys_roles_menus` VALUES (75, 1);
INSERT INTO `sys_roles_menus` VALUES (77, 1);
INSERT INTO `sys_roles_menus` VALUES (78, 1);
INSERT INTO `sys_roles_menus` VALUES (79, 1);
INSERT INTO `sys_roles_menus` VALUES (80, 1);
INSERT INTO `sys_roles_menus` VALUES (82, 1);
INSERT INTO `sys_roles_menus` VALUES (83, 1);
INSERT INTO `sys_roles_menus` VALUES (90, 1);
INSERT INTO `sys_roles_menus` VALUES (92, 1);
INSERT INTO `sys_roles_menus` VALUES (93, 1);
INSERT INTO `sys_roles_menus` VALUES (94, 1);
INSERT INTO `sys_roles_menus` VALUES (97, 1);
INSERT INTO `sys_roles_menus` VALUES (98, 1);
INSERT INTO `sys_roles_menus` VALUES (102, 1);
INSERT INTO `sys_roles_menus` VALUES (103, 1);
INSERT INTO `sys_roles_menus` VALUES (104, 1);
INSERT INTO `sys_roles_menus` VALUES (105, 1);
INSERT INTO `sys_roles_menus` VALUES (106, 1);
INSERT INTO `sys_roles_menus` VALUES (107, 1);
INSERT INTO `sys_roles_menus` VALUES (108, 1);
INSERT INTO `sys_roles_menus` VALUES (109, 1);
INSERT INTO `sys_roles_menus` VALUES (110, 1);
INSERT INTO `sys_roles_menus` VALUES (111, 1);
INSERT INTO `sys_roles_menus` VALUES (112, 1);
INSERT INTO `sys_roles_menus` VALUES (113, 1);
INSERT INTO `sys_roles_menus` VALUES (114, 1);
INSERT INTO `sys_roles_menus` VALUES (116, 1);
INSERT INTO `sys_roles_menus` VALUES (119, 1);
INSERT INTO `sys_roles_menus` VALUES (120, 1);
INSERT INTO `sys_roles_menus` VALUES (121, 1);
INSERT INTO `sys_roles_menus` VALUES (122, 1);
INSERT INTO `sys_roles_menus` VALUES (123, 1);
INSERT INTO `sys_roles_menus` VALUES (124, 1);
INSERT INTO `sys_roles_menus` VALUES (126, 1);
INSERT INTO `sys_roles_menus` VALUES (127, 1);
INSERT INTO `sys_roles_menus` VALUES (128, 1);
INSERT INTO `sys_roles_menus` VALUES (129, 1);
INSERT INTO `sys_roles_menus` VALUES (130, 1);
INSERT INTO `sys_roles_menus` VALUES (131, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (132, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (133, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (134, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (135, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (136, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (137, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (138, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (139, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (140, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (141, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (142, 1);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (143, 1);
INSERT INTO `sys_roles_menus` VALUES (1, 2);
INSERT INTO `sys_roles_menus` VALUES (2, 2);
INSERT INTO `sys_roles_menus` VALUES (6, 2);
INSERT INTO `sys_roles_menus` VALUES (7, 2);
INSERT INTO `sys_roles_menus` VALUES (9, 2);
INSERT INTO `sys_roles_menus` VALUES (10, 2);
INSERT INTO `sys_roles_menus` VALUES (11, 2);
INSERT INTO `sys_roles_menus` VALUES (14, 2);
INSERT INTO `sys_roles_menus` VALUES (15, 2);
INSERT INTO `sys_roles_menus` VALUES (19, 2);
INSERT INTO `sys_roles_menus` VALUES (21, 2);
INSERT INTO `sys_roles_menus` VALUES (23, 2);
INSERT INTO `sys_roles_menus` VALUES (30, 2);
INSERT INTO `sys_roles_menus` VALUES (32, 2);
INSERT INTO `sys_roles_menus` VALUES (33, 2);
INSERT INTO `sys_roles_menus` VALUES (34, 2);
INSERT INTO `sys_roles_menus` VALUES (36, 2);
INSERT INTO `sys_roles_menus` VALUES (80, 2);
INSERT INTO `sys_roles_menus` VALUES (82, 2);
INSERT INTO `sys_roles_menus` VALUES (83, 2);
INSERT INTO `sys_roles_menus` VALUES (116, 2);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (128, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (129, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (130, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (131, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (132, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (133, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (134, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (135, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (136, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (137, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (138, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (139, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (140, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (141, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (142, 3);
INSERT IGNORE INTO `sys_roles_menus` (`menu_id`, `role_id`) VALUES (143, 3);
COMMIT;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门名称',
  `username` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `gender` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性别',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `email` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `avatar_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像地址',
  `avatar_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像真实路径',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `is_admin` bit(1) DEFAULT b'0' COMMENT '是否为admin账号',
  `enabled` bit(1) DEFAULT NULL COMMENT '状态：1启用、0禁用',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `pwd_reset_time` datetime DEFAULT NULL COMMENT '修改密码的时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `UK_kpubos9gc2cvtkb0thktkbkes` (`email`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE,
  UNIQUE KEY `uniq_username` (`username`),
  UNIQUE KEY `uniq_email` (`email`),
  KEY `FK5rwmryny6jthaaxkogownknqp` (`dept_id`) USING BTREE,
  KEY `inx_enabled` (`enabled`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='系统用户';

-- ----------------------------
-- Records of sys_user
-- ----------------------------
BEGIN;
INSERT INTO `sys_user` VALUES (1, 2, 'admin', '管理员', '男', '18888888888', '<EMAIL>', 'avatar-20240927035738927.png', '/Users/<USER>/workspace/manage-java/~/avatar/avatar-20240927035738927.png', '$2a$10$Egp1/gvFlt7zhlXVfEFw4OfWQCGPw0ClmMcc6FjTnvXNRVf9zdMRa', b'1', b'1', NULL, 'admin', '2020-05-03 16:38:31', '2018-08-23 09:11:56', '2024-09-27 15:57:39');
INSERT INTO `sys_user` VALUES (2, 2, 'test', '测试', '男', '19999999999', '<EMAIL>', NULL, NULL, '$2a$10$4XcyudOYTSz6fue6KFNMHeUQnCX5jbBQypLEnGk1PmekXt5c95JcK', b'0', b'1', 'admin', 'admin', NULL, '2020-05-05 11:15:49', '2020-09-05 10:43:38');
INSERT INTO `sys_user` VALUES (3, 17, 'wangyuchen', 'wangyuchen', '男', '17629000035', '<EMAIL>', NULL, NULL, '$2a$10$.k1D5dFwMa5fjZc5P1NBVO4V4Xgya2YwVmzvs/nHVg4JXzYA6Uo2y', b'0', b'1', 'admin', 'wangyucehn', NULL, '2024-07-29 17:17:43', '2024-07-30 16:46:04');
INSERT INTO `sys_user` VALUES (4, 7, 'wangyuchen001', 'wangyuchen', '男', '17629000001', '<EMAIL>', NULL, NULL, '$2a$10$GRfcGRd/t/PHZY4QxzG0eOh9Iu9YMi2.SDmuczzxeto00Wrarmve2', b'0', b'1', 'wangyuchen', 'wangyuchen', NULL, '2024-07-30 16:47:22', '2024-07-30 16:47:22');
INSERT INTO `sys_user` VALUES (5, 2, 'panl', 'summer', '男', '13888888888', '<EMAIL>', NULL, NULL, '$2a$10$ZS8ex//2foBuNCLR.DSJO.QMTEg1Kn9vRJny7qhkPsDl6nT1awPTa', b'1', b'1', 'admin', 'panl', '2024-11-06 07:54:17', '2024-11-06 07:52:50', '2024-11-06 07:56:43');
INSERT IGNORE INTO `sys_user` (`user_id`, `dept_id`, `username`, `nick_name`, `gender`, `phone`, `email`, `avatar_name`, `avatar_path`, `password`, `is_admin`, `enabled`, `create_by`, `update_by`, `pwd_reset_time`, `create_time`, `update_time`) VALUES (8, 7, 'operation001', 'operation001', '男', '13299990001', '<EMAIL>', null, null, '$2a$10$1PUWvt8lCUTMwsm.73IfI.mEqw7tbt9b7oV9Lsw2wC6v8W1tgiJsO', 0, 1, null, null, null, '2025-05-21 08:47:57', '2025-05-21 08:48:07');
COMMIT;

-- ----------------------------
-- Table structure for sys_users_jobs
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_jobs`;
CREATE TABLE `sys_users_jobs` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `job_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`job_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Records of sys_users_jobs
-- ----------------------------
BEGIN;
INSERT INTO `sys_users_jobs` VALUES (1, 11);
INSERT INTO `sys_users_jobs` VALUES (2, 12);
INSERT INTO `sys_users_jobs` VALUES (3, 11);
INSERT INTO `sys_users_jobs` VALUES (4, 12);
INSERT INTO `sys_users_jobs` VALUES (5, 11);
COMMIT;

-- ----------------------------
-- Table structure for sys_users_roles
-- ----------------------------
DROP TABLE IF EXISTS `sys_users_roles`;
CREATE TABLE `sys_users_roles` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
  KEY `FKq4eq273l04bpu4efj0jd0jb98` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='用户角色关联';

-- ----------------------------
-- Records of sys_users_roles
-- ----------------------------
BEGIN;
INSERT INTO `sys_users_roles` VALUES (1, 1);
INSERT INTO `sys_users_roles` VALUES (3, 1);
INSERT INTO `sys_users_roles` VALUES (5, 1);
INSERT INTO `sys_users_roles` VALUES (2, 2);
INSERT INTO `sys_users_roles` VALUES (4, 2);
INSERT IGNORE INTO `sys_users_roles` VALUES (8, 3);
COMMIT;

-- ----------------------------
-- Table structure for task_code
-- ----------------------------
DROP TABLE IF EXISTS `task_code`;
CREATE TABLE `task_code` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据主键',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务事件',
  `main_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主任务事件',
  `inc` int DEFAULT NULL COMMENT '增量值',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务事件描述',
  `split_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拆分子事件',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modified_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务编号配置表';

-- ----------------------------
-- Records of task_code
-- ----------------------------
BEGIN;
INSERT INTO `task_code` VALUES ('c17d93948a044e139bbb9eb54bbf9412', 'acquire_question', 'acquire_question', 1, 'acquire question', NULL, '2024-09-26 20:16:56', '2024-09-26 20:16:56');
INSERT INTO `task_code` VALUES ('9fe9560506fd41f587328f7fbbfbbb7a', 'activity_lottery', 'activity_lottery', 1, 'activity_lottery', NULL, '2024-09-26 20:25:31', '2024-09-26 20:25:31');
INSERT INTO `task_code` VALUES ('2242caf2d29044c4a8c4b62ad14d8f30', 'alliance_initiation', 'alliance_initiation', 1, '联盟发起次数', NULL, NULL, '2025-03-19 10:14:13');
INSERT INTO `task_code` VALUES ('8cc65cd0df9a4221b7507058a5b0eeab', 'apply_referral', 'apply_referral', 1, '使用邀请码申请', 'apply_referral,invite_apply_referral_', '2024-12-03 12:57:00', '2024-12-03 12:57:00');
INSERT INTO `task_code` VALUES ('f1717cfe2e6a45c5a88cc83963e0a051', 'apply_wish', 'apply_wish', 1, 'apply_wish', 'apply_wish,invite_apply_wish_', '2024-09-26 20:11:03', '2025-03-28 14:33:27');
INSERT INTO `task_code` VALUES ('11b0d6c1a7ce4bafa1122bda68195e82', 'apply_wish_with_code', 'apply_wish_with_code', 1, 'apply_wish_with_code', NULL, '2024-09-26 20:11:11', '2024-09-26 20:11:11');
INSERT INTO `task_code` VALUES ('827134e00496444b9e0e1701546eb1f8', 'badge', 'badge', 1, 'mint badge', NULL, '2024-09-26 20:15:50', '2024-09-26 20:16:29');
INSERT INTO `task_code` VALUES ('447b0df625fc4598a7acc3df3ef204bd', 'carcosa_campaign', 'carcosa_campaign', 1, 'carcosa_campaign', NULL, '2025-03-10 03:08:32', '2025-03-10 03:08:32');
INSERT INTO `task_code` VALUES ('9f2ff439cc694c42b5c0355f7a1ce839', 'check_in', 'check_in', 1, 'check_in', NULL, '2025-03-07 03:27:25', '2025-03-07 03:27:25');
INSERT INTO `task_code` VALUES ('1bd0b4eb33b84e17a5c3351328376f3b', 'choose_winner', 'choose_winner', 1, 'choose_winner', NULL, '2024-09-26 20:10:50', '2024-09-26 20:10:50');
INSERT INTO `task_code` VALUES ('2ffd9ef9993b4d29b5023e0da9881a57', 'claim_badge', 'claim_badge', 1, 'claim_badge', NULL, '2024-09-26 20:13:36', '2024-09-26 20:13:36');
INSERT INTO `task_code` VALUES ('2cde464561864e15b5baa03965522b0c', 'claim_experience_voucher', 'claim_experience_voucher', 1, 'claim_experience_voucher', NULL, '2025-03-25 02:25:22', '2025-03-25 02:25:22');
INSERT INTO `task_code` VALUES ('39ef504518d9447d90bdcd77390f2734', 'claim_owo', 'claim_owo', 1, 'claim_owo', NULL, '2024-09-26 20:13:46', '2024-09-26 20:13:46');
INSERT INTO `task_code` VALUES ('a025eddf17564c7faa8ccbc09bec0dcc', 'claim_redeem', 'claim_redeem', 1, 'claim_redeem', NULL, '2024-09-26 20:21:00', '2024-09-26 20:21:00');
INSERT INTO `task_code` VALUES ('bafbafea545d444995ebb709b5413305', 'claim_rewards_app', 'claim_rewards_app', 1, 'clarim a rewards in app', NULL, '2024-09-26 20:25:22', '2024-09-26 20:25:22');
INSERT INTO `task_code` VALUES ('9def7b0faeb44bd79a43d774c306f639', 'claim_voucher', 'claim_voucher', 1, 'claim_voucher', NULL, '2025-03-25 02:25:10', '2025-03-25 02:25:10');
INSERT INTO `task_code` VALUES ('6de57b3de53845058f52d59490a34fd8', 'comment', 'comment', 1, 'comment', NULL, '2024-09-26 20:06:11', '2024-09-26 20:06:11');
INSERT INTO `task_code` VALUES ('4febf60120f34796a1305aafdc375995', 'comment_post_x', 'comment_post_x', 1, 'Comment Post X', NULL, '2024-09-26 19:46:36', '2024-09-26 19:46:36');
INSERT INTO `task_code` VALUES ('e8a6eac619ef4631a190b50685a4eb10', 'community_to_space', 'community_to_space', 1, 'tribe 升级 space', NULL, NULL, '2024-12-12 08:24:37');
INSERT INTO `task_code` VALUES ('8db25ef0712e420b958792bb338ac139', 'compensation', 'compensation', 1, 'compensation', NULL, '2024-09-26 20:13:18', '2024-09-26 20:13:18');
INSERT INTO `task_code` VALUES ('600ce2824931499b957632e4501e980a', 'connect_dc', 'connect_dc', 1, 'Connect DC', NULL, '2024-09-26 19:34:27', '2024-09-26 19:34:27');
INSERT INTO `task_code` VALUES ('ef2361a840a34182985be83b74e0f465', 'connect_tg', 'connect_tg', 1, 'connect_tg', NULL, '2024-12-10 06:14:29', '2024-12-10 06:14:29');
INSERT INTO `task_code` VALUES ('d00fab4c6f4845ff8ac195ebbd97b78a', 'connect_wallet', 'connect_wallet', 1, 'connect_wallet', NULL, '2024-09-26 20:19:51', '2024-09-26 20:19:51');
INSERT INTO `task_code` VALUES ('04a89c439c674b99aa7160a9f6d48ca6', 'connect_x', 'connect_x', 1, 'Connect X', NULL, '2024-09-26 19:33:46', '2024-09-26 19:33:46');
INSERT INTO `task_code` VALUES ('cf7e08ce19d247a68bd20950d103a682', 'create_deek_profile', 'create_deek_profile', 1, 'create_deek_profile', NULL, '2024-12-05 10:17:34', '2024-12-05 10:17:34');
INSERT INTO `task_code` VALUES ('60131dd468b642c09f80ca7844552198', 'created_tribe', 'created_tribe', 1, 'created_tribe', NULL, '2024-09-26 20:07:06', '2024-09-26 20:07:06');
INSERT INTO `task_code` VALUES ('db912ac533e947bdb88d037386e4ad7b', 'daily_barbarian_wins', 'daily_barbarian_wins', 1, '用户当日战胜野蛮人数量', NULL, NULL, '2025-03-19 10:14:23');
INSERT INTO `task_code` VALUES ('e6a3ab540c02457aacd9aa1db9484e4b', 'daily_gold_mine_count', 'daily_gold_mine_count', 1, '当日采集金矿次数', NULL, NULL, '2025-03-19 10:14:51');
INSERT INTO `task_code` VALUES ('24d0f836111e4d91a70ba1f9fa47be8f', 'daily_pvp_count', 'daily_pvp_count', 1, '当日pvp次数', NULL, NULL, '2025-03-19 10:14:41');
INSERT INTO `task_code` VALUES ('858694bfa3394e47a732c35c3b8f18f2', 'daily_rally_count', 'daily_rally_count', 1, '当日集结次数', NULL, NULL, '2025-03-19 10:14:32');
INSERT INTO `task_code` VALUES ('dc6dcdae11ae46cc82e8bf40c3febc49', 'early_bird', 'early_bird', 1, 'early_bird', NULL, '2024-09-26 20:10:30', '2024-09-26 20:10:30');
INSERT INTO `task_code` VALUES ('881129e245f14def822b361f7dc95cfe', 'earnalliance_registration', 'earnalliance_registration', 1, 'earnalliance_registration', NULL, '2025-04-03 11:26:35', '2025-04-03 11:26:35');
INSERT INTO `task_code` VALUES ('********************************', 'endurance_test', 'endurance_test', 1, 'endurance_test', NULL, '2025-03-10 03:09:16', '2025-03-10 03:09:16');
INSERT INTO `task_code` VALUES ('68e6d6bc43d44f0d8e88878755991c09', 'eoa_asset_cellulabitlife', 'eoa_asset_cellulabitlife', 1, 'eoa_asset_cellulabitlife', NULL, '2024-11-15 20:55:08', '2024-11-15 20:55:08');
INSERT INTO `task_code` VALUES ('64ca20c07c1a46b582d3967406e23253', 'eoa_asset_ETH', 'eoa_asset_ETH', 1, 'eoa_asset_ETH', NULL, '2024-12-05 09:53:04', '2024-12-05 09:53:04');
INSERT INTO `task_code` VALUES ('6528aac686f54df8961afae0e0a5c25b', 'eoa_asset_fireflypass', 'eoa_asset_fireflypass', 1, 'eoa_asset_fireflypass', NULL, '2024-09-26 20:25:46', '2024-09-26 20:25:46');
INSERT INTO `task_code` VALUES ('6285548f9ccd45fdbb3d74eb9a03b343', 'eoa_asset_gensokishionlinev2', 'eoa_asset_gensokishionlinev2', 1, 'eoa_asset_gensokishionlinev2', NULL, '2024-12-05 09:44:43', '2024-12-05 09:44:43');
INSERT INTO `task_code` VALUES ('2a8e9e4be56b4e5d8165bb7e685245b6', 'eoa_asset_lilquid', 'eoa_asset_lilquid', 1, 'eoa_asset_lilquid', NULL, '2024-12-20 05:50:59', '2024-12-20 05:50:59');
INSERT INTO `task_code` VALUES ('8ee92b2b992d49b68ff2259989bc3e0d', 'eoa_asset_OWOG', 'eoa_asset_OWOG', 1, 'eoa_asset_OWOG', NULL, '2024-12-05 09:51:53', '2024-12-05 09:51:53');
INSERT INTO `task_code` VALUES ('62987d0293d647fcac10e63edfa38016', 'eoa_asset_persona_base', 'eoa_asset_persona_base', 1, 'eoa_asset_persona_base', NULL, '2024-12-05 09:45:54', '2024-12-05 09:45:54');
INSERT INTO `task_code` VALUES ('c4b9e194aabd4ae2acd5a18866c31a88', 'eoa_asset_SOBA', 'eoa_asset_SOBA', 1, 'eoa_asset_SOBA', NULL, '2024-12-05 09:49:51', '2024-12-05 09:49:51');
INSERT INTO `task_code` VALUES ('fc2259650a494bb584fbe56de6ce2bd2', 'eoa_asset_spaceid', 'eoa_asset_spaceid', 1, 'eoa_asset_spaceid', NULL, '2024-09-26 20:25:38', '2024-09-26 20:25:38');
INSERT INTO `task_code` VALUES ('a2f4f97426694925915664917a362250', 'eoa_asset_USDC', 'eoa_asset_USDC', 1, 'eoa_asset_USDC', NULL, '2024-12-05 09:55:08', '2024-12-05 09:55:08');
INSERT INTO `task_code` VALUES ('bbf5dda91e014947be8692169a9942e9', 'eoa_asset_wand', 'eoa_asset_wand', 1, 'eoa_asset_wand', NULL, '2024-12-20 05:49:47', '2024-12-20 05:49:47');
INSERT INTO `task_code` VALUES ('22c8b41053114c18991b1d822d28c700', 'external_reward', 'external_reward', 1, 'external reward', NULL, '2024-09-26 20:19:08', '2024-09-26 20:19:08');
INSERT INTO `task_code` VALUES ('e7f4e537ed25438ab15259bf9e6e44ec', 'finish_follow_bounty_app', 'finish_follow_bounty_app', 1, 'finish follow bounty in app', NULL, '2024-09-26 20:24:51', '2024-09-26 20:24:51');
INSERT INTO `task_code` VALUES ('09d6d51fe91848699a09355846dbfa53', 'follow', 'follow', 1, '关注', 'follow,followed_', NULL, '2024-12-02 18:07:13');
INSERT INTO `task_code` VALUES ('b254c60ced6647e38ca354c068587362', 'follow_deeker_app', 'follow_deeker_app', 1, 'follow deeker in app', 'follow_deeker_app,followed_deeker_app_', '2024-09-26 20:24:33', '2024-12-04 09:01:53');
INSERT INTO `task_code` VALUES ('edcb0e582bd84cd1b8bf37bc92749f37', 'follow_x', 'follow_x', 1, 'Follow X', NULL, '2024-09-26 19:34:10', '2024-09-26 19:34:10');
INSERT INTO `task_code` VALUES ('eb8143d94a934c5a96bc324c34bc2af2', 'followed', 'followed', 1, '被关注', NULL, '2024-12-02 10:10:10', '2024-12-02 10:10:10');
INSERT INTO `task_code` VALUES ('0c924cb9daac49aeaef3d5be3ff3d426', 'followed_deeker_app', 'followed_deeker_app', 1, '被关注', NULL, '2024-12-04 09:03:02', '2024-12-04 09:03:02');
INSERT INTO `task_code` VALUES ('5ae9eb7dd20d4f62908c7dc2ea1fc7b6', 'game_over', 'game_over', 1, 'game_over', NULL, '2025-04-03 06:32:21', '2025-04-03 06:32:21');
INSERT INTO `task_code` VALUES ('f382d00951544cf19cf6ddf1faca889c', 'invite_apply_referral', 'invite_apply_referral', 1, '邀请他人申请（邀请码被使用）', NULL, '2024-12-04 03:40:17', '2024-12-04 03:40:17');
INSERT INTO `task_code` VALUES ('df1b24d8d58a4f94bc450dbf58ba49ca', 'invite_apply_wish', 'invite_apply_wish', 1, 'invite_apply_wish', NULL, '2024-12-05 08:56:52', '2024-12-05 08:56:52');
INSERT INTO `task_code` VALUES ('f597ca12f6234226aaec4ab3760dbe63', 'invite_code_regist_app', 'invite_code_regist_app', 1, 'invite code regist app', NULL, '2024-09-26 20:14:57', '2024-09-26 20:14:57');
INSERT INTO `task_code` VALUES ('28f6809497de473bbfef129d1f0f0dd3', 'invite_code_verification', 'invite_code_verification', 1, 'invite code verification', NULL, '2024-09-26 20:12:27', '2024-09-26 20:12:50');
INSERT INTO `task_code` VALUES ('1c04c9ab45f04f1b9083949e7e58dd06', 'invite_code_verification_app', 'invite_code_verification_app', 1, 'invite_code_verification_app', NULL, '2024-09-26 20:14:39', '2024-09-26 20:14:39');
INSERT INTO `task_code` VALUES ('e0ca6dad8603456ba923ec3cc93373ca', 'invite_mint_nft', 'invite_mint_nft', 1, 'invite_mint_nft', NULL, '2024-09-26 20:13:05', '2024-09-26 20:13:05');
INSERT INTO `task_code` VALUES ('5108a5a575a642fcae46bab366fdfd07', 'invited_register', 'invited_register', 1, 'invited_register', NULL, '2024-09-26 20:09:10', '2024-09-26 20:09:10');
INSERT INTO `task_code` VALUES ('cae344f16f934ace9cde54cffa817a9a', 'join_dc', 'join_dc', 1, 'JOIN DC', NULL, '2024-09-26 19:34:45', '2024-09-26 19:34:45');
INSERT INTO `task_code` VALUES ('5e5d69df4e9e45bbb9228c5cc1855efc', 'join_game', 'join_game', 1, 'join_game', NULL, '2024-09-26 19:47:26', '2024-09-26 19:47:26');
INSERT INTO `task_code` VALUES ('a802078c55d24662b2735a09a3fb3d51', 'join_tg', 'join_tg', 1, 'join_tg', NULL, '2024-09-26 20:19:30', '2024-09-26 20:19:30');
INSERT INTO `task_code` VALUES ('b25b0f9f01f1406eb78e5a28ace79064', 'join_tg_channel', 'join_tg_channel', 1, 'join tg channel', NULL, '2024-09-26 20:18:56', '2024-09-26 20:18:56');
INSERT INTO `task_code` VALUES ('c316117a45da437b98c97082df632625', 'join_tg_group', 'join_tg_group', 1, 'join_tg_group', NULL, '2025-02-06 06:27:56', '2025-02-06 06:27:56');
INSERT INTO `task_code` VALUES ('224e7048db4a4756baeef9844f6fccf2', 'join_tribe', 'join_tribe', 1, 'join_tribe', 'join_tribe,tribe_join_member_', '2024-09-26 20:06:42', '2024-11-11 06:32:01');
INSERT INTO `task_code` VALUES ('8826bd6089594362b8270f3b849b6eef', 'kingdom_progression', 'kingdom_progression', 1, 'kingdom_progression', NULL, '2025-03-07 10:17:07', '2025-03-07 10:17:07');
INSERT INTO `task_code` VALUES ('009ab98950f442d1a80c331a062dfeda', 'like', 'like', 1, 'like', 'like,liked_', '2024-09-26 20:05:16', '2024-11-11 06:26:01');
INSERT INTO `task_code` VALUES ('e3175a67f9ca468aa3999b0b85678b41', 'like_post_x', 'like_post_x', 1, 'Like Post X', NULL, '2024-09-26 19:46:19', '2024-09-26 19:46:19');
INSERT INTO `task_code` VALUES ('9c42bf0ee7c14f538e7acdbd10497b19', 'liked', 'liked', 1, 'liked', NULL, '2024-09-26 20:06:02', '2024-09-26 20:06:02');
INSERT INTO `task_code` VALUES ('eb7141bc428c40a0af6024502dcbbb67', 'main_city_level', 'main_city_level', 1, '用户当前主城等级', NULL, NULL, '2025-03-19 10:13:28');
INSERT INTO `task_code` VALUES ('b69568b58d024598b977a30a5c3eed41', 'make_wish', 'make_wish', 1, 'make_wish', NULL, '2024-09-26 20:10:39', '2024-09-26 20:10:39');
INSERT INTO `task_code` VALUES ('4a0a2534db83455781e5e103ace5ca35', 'manual_reward', 'manual_reward', 1, 'manual_reward', NULL, '2024-09-26 20:16:00', '2024-09-26 20:16:00');
INSERT INTO `task_code` VALUES ('c8b3e4d37ed84c409f43a04ba10c7af0', 'megaphone', 'megaphone', 1, 'megaphone', NULL, '2024-09-26 20:05:27', '2024-09-26 20:05:27');
INSERT INTO `task_code` VALUES ('b2020fd33e2449c7a588719333af2ba9', 'mint_nft', 'mint_nft', 1, 'mint_nft', NULL, '2024-09-26 20:11:59', '2024-09-26 20:11:59');
INSERT INTO `task_code` VALUES ('f0c60e82424844749154bd7dd512b32b', 'name_x', 'name_x', 1, 'name_x', NULL, '2024-09-26 20:11:44', '2024-09-26 20:11:44');
INSERT INTO `task_code` VALUES ('94577c06229e43e6a39fa73c1af3a360', 'open_reaction_vote_1', 'open_reaction_vote_1', 1, 'open_reaction_vote_1', 'like,liked_', '2024-11-11 06:40:36', '2024-11-11 06:40:36');
INSERT INTO `task_code` VALUES ('1bc6360e01b84f61ab8d69e5b22e9099', 'osp_callback', 'osp_callback', 1, 'osp callback', NULL, '2024-09-26 20:26:36', '2024-09-26 20:26:36');
INSERT INTO `task_code` VALUES ('66f09ffbf04b4bec922cd9f6508c5ecb', 'osp_connect_tg', 'osp_connect_tg', 1, 'osp_connect_tg', NULL, '2024-09-26 20:19:43', '2024-09-26 20:19:43');
INSERT INTO `task_code` VALUES ('e0272b8ca49b48f59112cd8454e91981', 'osp_profile_create', 'osp_profile_create', 1, 'osp_profile_create', NULL, '2024-09-26 20:12:09', '2024-09-26 20:12:09');
INSERT INTO `task_code` VALUES ('9ebdc12df91842dbb47c332e1cb2f012', 'owo_asset', 'owo_asset', 1, 'owo_asset', NULL, '2024-09-26 20:14:22', '2024-09-26 20:14:22');
INSERT INTO `task_code` VALUES ('7a98fab65656459cbf1ef52d4f57fa7d', 'owo_asset_over_limit', 'owo_asset_over_limit', 1, 'owo_asset_over_limit', NULL, '2024-12-05 09:56:46', '2024-12-05 09:56:46');
INSERT INTO `task_code` VALUES ('e7911799970e40c6bfec2593be5aea97', 'play_game', 'play_game', 1, 'play_game', 'play_game,invited_play_game_', '2024-09-26 19:47:38', '2025-04-03 07:33:40');
INSERT INTO `task_code` VALUES ('05b0a83ee82c422c80889552acd2f626', 'post', 'post', 1, 'post', NULL, '2024-09-26 20:05:55', '2024-09-26 20:05:55');
INSERT INTO `task_code` VALUES ('b1095a553faa414b9732904245d06314', 'post_follow_bounty_app', 'post_follow_bounty_app', 1, 'post a follow bounty in app', NULL, '2024-09-26 20:25:06', '2024-09-26 20:25:06');
INSERT INTO `task_code` VALUES ('82dde52e2a5e49caba9d9ce474b0745d', 'power_journey', 'power_journey', 1, 'power_journey', NULL, NULL, '2025-03-10 03:07:41');
INSERT INTO `task_code` VALUES ('4e902bbefd584b9195cd0d5ac01d7b16', 'purchase_membership', 'purchase_membership', 1, 'purchase_membership', NULL, '2024-09-26 20:06:24', '2024-09-26 20:06:24');
INSERT INTO `task_code` VALUES ('16579c965a1042afa885794d4f7a22ba', 'refer_apply', 'refer_apply', 1, 'refer_apply', NULL, '2024-09-26 20:11:22', '2024-09-26 20:11:22');
INSERT INTO `task_code` VALUES ('eb536d84d3614322a940de2fc698ad01', 'registration', 'registration', 1, 'registration', 'registration,invited_register_', '2024-09-26 20:09:20', '2024-11-11 06:30:13');
INSERT INTO `task_code` VALUES ('c4a8ab5a60694c62a3d073f9f9004b15', 'reply_post_x', 'reply_post_x', 1, 'reply_post_x', NULL, '2024-09-26 20:07:26', '2024-09-26 20:07:26');
INSERT INTO `task_code` VALUES ('036f1dd55f794b70bbbfcc0f8b3654a6', 'retweet_post_x', 'retweet_post_x', 1, 'Retweet Post X', NULL, '2024-09-26 19:46:51', '2024-09-26 19:46:51');
INSERT INTO `task_code` VALUES ('54bbdc8484ff41228d3fefde8d2fc495', 'sets_calculate_reward', 'sets_calculate_reward', 1, 'sets calculate reward', NULL, '2024-09-26 20:18:26', '2024-09-26 20:18:26');
INSERT INTO `task_code` VALUES ('ddf53a974ebf41a6bef1248c241c4b0b', 'sets_nature_reward', 'sets_nature_reward', 1, 'sets nature reward', NULL, '2024-09-26 20:18:43', '2024-09-26 20:18:43');
INSERT INTO `task_code` VALUES ('5de2fb171a6548e5be94ebc18fe0d120', 'sign_in', 'sign_in', 1, 'sign_in', NULL, '2024-09-26 19:47:04', '2024-09-26 19:47:04');
INSERT INTO `task_code` VALUES ('4cc64502b1af462b8e837e17110b7d39', 'skip_verify', 'skip_verify', 1, 'skip_verify', NULL, '2024-09-26 20:14:31', '2024-09-26 20:14:31');
INSERT INTO `task_code` VALUES ('1ecc72b419f74926999b02820e82f7c5', 'start_game_deek', 'start_game_deek', 1, 'start game deek', NULL, '2024-09-26 20:20:04', '2024-09-26 20:20:04');
INSERT INTO `task_code` VALUES ('b1b5263c2b994ee4aca8a02f6061ec72', 'start_game_loa', 'start_game_loa', 1, 'start game loa', NULL, '2024-09-26 20:20:19', '2024-09-26 20:20:19');
INSERT INTO `task_code` VALUES ('18a6599ff9aa49c8b91a6c635e103711', 'subject_post_x', 'subject_post_x', 1, 'subject_post_x', NULL, '2024-09-26 20:07:14', '2024-09-26 20:07:14');
INSERT INTO `task_code` VALUES ('71a8c2f1573547e981741a5c3518324c', 'submit_question', 'submit_question', 1, 'submit question', NULL, '2024-09-26 20:18:12', '2024-09-26 20:18:12');
INSERT INTO `task_code` VALUES ('731dbf756b524bbba4e64f79b5d0139f', 'test', 'test', 1, 'test', NULL, NULL, '2024-11-15 17:51:26');
INSERT INTO `task_code` VALUES ('90ba9a2006df473db6df35b74ac06c51', 'test1', 'test1', 1, 'test1', NULL, '2024-09-30 09:49:11', '2024-09-30 09:49:11');
INSERT INTO `task_code` VALUES ('897f8c1bda5443f0a317671762709963', 'tg_connect_auth', 'tg_connect_auth', 1, 'tg上绑定web3auth', NULL, '2024-12-04 06:06:08', '2024-12-04 06:06:08');
INSERT INTO `task_code` VALUES ('3d7a2f6946aa453ead7ff5f992f6d508', 'total_barbarian_kills', 'total_barbarian_kills', 1, '累计击杀野蛮人数量', NULL, NULL, '2025-03-19 10:13:52');
INSERT INTO `task_code` VALUES ('33ad5dbec018454b95df5a3aaa4dbb5e', 'total_power', 'total_power', 1, '用户总战力', NULL, NULL, '2025-03-19 10:13:41');
INSERT INTO `task_code` VALUES ('c5f808a11e164a41a8c39a25226db9e5', 'total_vit_consumed', 'total_vit_consumed', 1, '累计消耗vit', NULL, NULL, '2025-03-19 10:14:02');
INSERT INTO `task_code` VALUES ('8de43914d66c414191d604b2781ceca3', 'trade_wish', 'trade_wish', 1, 'trade wish', NULL, '2024-09-26 20:15:25', '2024-09-26 20:15:25');
INSERT INTO `task_code` VALUES ('d206411bc837419d922fe93e53ef0765', 'tribe_join_member', 'tribe_join_member', 1, 'tribe_join_member', NULL, '2024-09-26 20:06:55', '2024-09-26 20:06:55');
INSERT INTO `task_code` VALUES ('5bf85be9226047d4a2ed5e19b7aef34e', 'tribe_recommended', 'tribe_recommended', 1, 'tribe_recommended', NULL, '2024-09-26 20:21:15', '2024-09-26 20:21:15');
INSERT INTO `task_code` VALUES ('49dfa3fb0da74b3db84b234283392c50', 'tribe_to_space', 'tribe_to_space', 1, 'tribe_to_space', NULL, '2024-12-06 08:08:09', '2024-12-06 08:08:09');
INSERT INTO `task_code` VALUES ('a6b9fb4f273b49f8bec29c13f973dbbe', 'unlock_wish', 'unlock_wish', 1, 'unlock wish', NULL, '2024-09-26 20:15:12', '2024-09-26 20:15:12');
INSERT INTO `task_code` VALUES ('fa5c9ae064f5408ab28b27672cecf75b', 'vampire_attack', 'vampire_attack', 1, 'vampire_attack', NULL, '2024-09-26 20:09:32', '2024-09-26 20:09:32');
INSERT INTO `task_code` VALUES ('e332c10d22854e1db1e3ca9313b1d790', 'visit_website', 'visit_website', 1, 'visit_website', NULL, '2024-09-26 20:12:18', '2024-09-26 20:12:18');
INSERT INTO `task_code` VALUES ('1f50d763d75340978b839a141d0dc779', 'wallet_asset', 'wallet_asset', 1, 'wallet_asset', NULL, '2024-09-26 20:07:45', '2024-09-26 20:07:45');
INSERT INTO `task_code` VALUES ('64af48b7c2b840dfb670eb58a4f8022f', 'wallet_bind', 'wallet_bind', 1, 'wallet_bind', NULL, '2024-09-26 20:07:36', '2024-09-26 20:07:36');
INSERT INTO `task_code` VALUES ('223da1199ca44ae3b505424a4218cf4c', 'wallet_deposit', 'wallet_deposit', 1, 'wallet_deposit', NULL, '2024-09-26 20:09:02', '2024-09-26 20:09:02');
INSERT INTO `task_code` VALUES ('39476f172ff14e0c98ac97f54d5432f6', 'win_answers', 'win_answers', 1, '收到回答', NULL, '2024-12-02 10:12:26', '2024-12-02 10:12:26');
INSERT INTO `task_code` VALUES ('07847d7894864689a7525e28c0cf8851', 'win_offer', 'win_offer', 1, 'win_offer', NULL, '2024-09-26 20:11:34', '2024-09-26 20:11:34');
INSERT INTO `task_code` VALUES ('24c7b2bccda240899b720995ca49cbee', 'x_authorize', 'x_authorize', 1, 'x authorize', NULL, '2024-09-26 20:15:36', '2024-09-26 20:15:36');
INSERT INTO `task_code` VALUES ('2b613a1496164ee9b35ec27cc6f38ef9', 'x_view', 'x_view', 1, 'twitter浏览量任务', NULL, '2025-01-24 10:12:09', '2025-01-24 10:12:09');
COMMIT;

-- ----------------------------
-- Table structure for task_config
-- ----------------------------
DROP TABLE IF EXISTS `task_config`;
CREATE TABLE `task_config` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据主键',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'appId',
  `task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务id',
  `show_list` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否列表展示(0 否 1 是)',
  `ledger_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '积分流水描述',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务标题',
  `desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务描述',
  `limit_count_normal` int DEFAULT NULL COMMENT '任务次数上限(非会员)',
  `limit_count_l1` int DEFAULT NULL COMMENT '任务次数上限(会员L1)',
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务状态(ACTIVE、DISABLE)',
  `start_time` datetime DEFAULT NULL COMMENT '任务开始时间，utc毫秒时间戳',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间，utc毫秒时间戳',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务事件编号',
  `reward_frequency` int DEFAULT NULL COMMENT '发奖频率，每完成n次任务，发一次奖',
  `cycle` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务刷新周期',
  `rewards` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '奖励数量',
  `order` int DEFAULT NULL COMMENT '任务排序',
  `domain` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务所属模块twitter、discord',
  `vip_level` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务要求的vip等级，+0: 普通用户以上，1: 会员专享',
  `btn` int DEFAULT NULL COMMENT '前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮',
  `show_progress` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '是否显示任务showProgress进度条',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reward_amount` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reward_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reward_currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'reward_currency',
  `publish_time` datetime DEFAULT NULL COMMENT '计划发布时间',
  `show_reward_amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `attr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '额外属性',
  `connect_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '授权url',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务配置表';

-- ----------------------------
-- Records of task_config
-- ----------------------------


-- ----------------------------
-- Table structure for task_icon
-- ----------------------------
DROP TABLE IF EXISTS `task_icon`;
CREATE TABLE `task_icon` (
  `id` varchar(255) DEFAULT NULL,
  `saas_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- ----------------------------
-- Records of task_icon
-- ----------------------------
BEGIN;
INSERT INTO `task_icon` VALUES ('4ebcd23d003a42a08e8e0948f95f5c7e', 'monster', 'code', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/monster/code.webp');
INSERT INTO `task_icon` VALUES ('e0a569b0dc3f4422b3e1522e986f889c', 'monster', 'gf', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/monster/gf.webp');
INSERT INTO `task_icon` VALUES ('54a383b7f42b457d8844bd07d06f8c30', 'dojo3-tg', 'check_in', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/dojo3-tg/check_in.svg');
INSERT INTO `task_icon` VALUES ('968c31b6e1a343a6a335e286f93d17f7', 'dojo3-tg', 'play_game', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/dojo3-tg/play_game.svg');
INSERT INTO `task_icon` VALUES ('a924a09d8fed4c2fa7e8e27c601c0464', 'dojo3-tg', 'subscribe_tg', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/dojo3-tg/subscribe_tg.svg');
INSERT INTO `task_icon` VALUES ('61f2b201113f4be7b4d65bb8812ce16e', 'deek-tg', 'play_deek_game_shake', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/deek-tg/play_deek_game_shake.png');
INSERT INTO `task_icon` VALUES ('239b608846344d69a84960ed25e076af', 'deek-tg', 'claim_once_harvest', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/deek-tg/claim_once_harvest.png');
INSERT INTO `task_icon` VALUES ('d2fe975db7e246518ac18a36e81d4112', 'deek-tg', 'follow_x_deek', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/deek-tg/follow_x_deek.png');
INSERT INTO `task_icon` VALUES ('9601db5340dc4dd0a783ab96124b2520', 'dojo3', 'connect_tg', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/dojo3/connect_tg.webp');
INSERT INTO `task_icon` VALUES ('57fa1a8f802141e091f881c9960c5fc6', 'deek-tg', 'create_deek_profile', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/deek-tg/create_deek_profile.png');
INSERT INTO `task_icon` VALUES ('0109a96b827b47f4b1722225745451e3', 'mugen', 'main_city_level', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/main_city_level.webp');
INSERT INTO `task_icon` VALUES ('4937b10756494853b67b652e81a712b0', 'mugen', 'total_power', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/total_power.webp');
INSERT INTO `task_icon` VALUES ('bc9881043f27466b9412715d4dbf66e9', 'mugen', 'total_barbarian_kills', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/total_barbarian_kills.webp');
INSERT INTO `task_icon` VALUES ('d205fa3e58b34d7eba31138e74cd5744', 'mugen', 'total_vit_consumed', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/total_vit_consumed.webp');
INSERT INTO `task_icon` VALUES ('6957084542a7404babc2fb0071f093e2', 'mugen', 'daily_barbarian_wins', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/daily_barbarian_wins.webp');
INSERT INTO `task_icon` VALUES ('23ac329df9ef43f0ad171435bed0f578', 'mugen', 'daily_rally_count', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/daily_rally_count.webp');
INSERT INTO `task_icon` VALUES ('890aacc809d349d68785b4777ac83e21', 'mugen', 'daily_pvp_count', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/daily_pvp_count.webp');
INSERT INTO `task_icon` VALUES ('b9ad79db4e2a4e8e847c60e7acf1adb6', 'mugen', 'daily_gold_mine_count', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/daily_gold_mine_count.webp');
INSERT INTO `task_icon` VALUES ('0ba7971c68c5487083085d436ff3fe86', 'mugen', 'alliance_initiation', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/alliance_initiation.webp');
INSERT INTO `task_icon` VALUES ('dc80fa5e01b14164bea4d5e96c5238b0', 'mugen', 'subject_post_x', 'https://quests-dev.oss-ap-southeast-1.aliyuncs.com/quests/mugen/subject_post_x.webp');
COMMIT;

-- ----------------------------
-- Table structure for task_whitelist
-- ----------------------------
DROP TABLE IF EXISTS `task_whitelist`;
CREATE TABLE `task_whitelist` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
  `saas_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'SaaS ID',
  `flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户id',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='Task Whitelist';

-- ----------------------------
-- Records of task_whitelist
-- ----------------------------
BEGIN;
INSERT INTO `task_whitelist` VALUES ('0045d9de901f4a76a19f6af372a3edda', 'bread', '2024101802392793101004', '驾临');
INSERT INTO `task_whitelist` VALUES ('40c999d60ae54405bfe7bd316aabd6ac', 'zeek-app', 'Test04', NULL);
INSERT INTO `task_whitelist` VALUES ('c324d3e5d90949fd98d1e8dd201f8083', 'mugen', '11111', 'cc');
INSERT INTO `task_whitelist` VALUES ('daf2c919856742369ed0a6b5579c743b', 'dojo3-tg', 'Test05', NULL);
INSERT INTO `task_whitelist` VALUES ('ff780ee1a9874115a5a688cb20490632', 'mugen', '2024112210482762031805', NULL);
COMMIT;

-- ----------------------------
-- Table structure for tool_alipay_config
-- ----------------------------
DROP TABLE IF EXISTS `tool_alipay_config`;
CREATE TABLE `tool_alipay_config` (
  `config_id` bigint NOT NULL COMMENT 'ID',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用ID',
  `charset` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '编码',
  `format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型 固定格式json',
  `gateway_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网关地址',
  `notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异步回调',
  `private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '私钥',
  `public_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '公钥',
  `return_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回调地址',
  `sign_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '签名方式',
  `sys_service_provider_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商户号',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='支付宝配置类';

-- ----------------------------
-- Records of tool_alipay_config
-- ----------------------------
BEGIN;
INSERT INTO `tool_alipay_config` VALUES (1, '2016091700532697', 'utf-8', 'JSON', 'https://openapi.alipaydev.com/gateway.do', 'http://api.auauz.net/api/aliPay/notify', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC5js8sInU10AJ0cAQ8UMMyXrQ+oHZEkVt5lBwsStmTJ7YikVYgbskx1YYEXTojRsWCb+SH/kDmDU4pK/u91SJ4KFCRMF2411piYuXU/jF96zKrADznYh/zAraqT6hvAIVtQAlMHN53nx16rLzZ/8jDEkaSwT7+HvHiS+7sxSojnu/3oV7BtgISoUNstmSe8WpWHOaWv19xyS+Mce9MY4BfseFhzTICUymUQdd/8hXA28/H6osUfAgsnxAKv7Wil3aJSgaJczWuflYOve0dJ3InZkhw5Cvr0atwpk8YKBQjy5CdkoHqvkOcIB+cYHXJKzOE5tqU7inSwVbHzOLQ3XbnAgMBAAECggEAVJp5eT0Ixg1eYSqFs9568WdetUNCSUchNxDBu6wxAbhUgfRUGZuJnnAll63OCTGGck+EGkFh48JjRcBpGoeoHLL88QXlZZbC/iLrea6gcDIhuvfzzOffe1RcZtDFEj9hlotg8dQj1tS0gy9pN9g4+EBH7zeu+fyv+qb2e/v1l6FkISXUjpkD7RLQr3ykjiiEw9BpeKb7j5s7Kdx1NNIzhkcQKNqlk8JrTGDNInbDM6inZfwwIO2R1DHinwdfKWkvOTODTYa2MoAvVMFT9Bec9FbLpoWp7ogv1JMV9svgrcF9XLzANZ/OQvkbe9TV9GWYvIbxN6qwQioKCWO4GPnCAQKBgQDgW5MgfhX8yjXqoaUy/d1VjI8dHeIyw8d+OBAYwaxRSlCfyQ+tieWcR2HdTzPca0T0GkWcKZm0ei5xRURgxt4DUDLXNh26HG0qObbtLJdu/AuBUuCqgOiLqJ2f1uIbrz6OZUHns+bT/jGW2Ws8+C13zTCZkZt9CaQsrp3QOGDx5wKBgQDTul39hp3ZPwGNFeZdkGoUoViOSd5Lhowd5wYMGAEXWRLlU8z+smT5v0POz9JnIbCRchIY2FAPKRdVTICzmPk2EPJFxYTcwaNbVqL6lN7J2IlXXMiit5QbiLauo55w7plwV6LQmKm9KV7JsZs5XwqF7CEovI7GevFzyD3w+uizAQKBgC3LY1eRhOlpWOIAhpjG6qOoohmeXOphvdmMlfSHq6WYFqbWwmV4rS5d/6LNpNdL6fItXqIGd8I34jzql49taCmi+A2nlR/E559j0mvM20gjGDIYeZUz5MOE8k+K6/IcrhcgofgqZ2ZED1ksHdB/E8DNWCswZl16V1FrfvjeWSNnAoGAMrBplCrIW5xz+J0Hm9rZKrs+AkK5D4fUv8vxbK/KgxZ2KaUYbNm0xv39c+PZUYuFRCz1HDGdaSPDTE6WeWjkMQd5mS6ikl9hhpqFRkyh0d0fdGToO9yLftQKOGE/q3XUEktI1XvXF0xyPwNgUCnq0QkpHyGVZPtGFxwXiDvpvgECgYA5PoB+nY8iDiRaJNko9w0hL4AeKogwf+4TbCw+KWVEn6jhuJa4LFTdSqp89PktQaoVpwv92el/AhYjWOl/jVCm122f9b7GyoelbjMNolToDwe5pF5RnSpEuDdLy9MfE8LnE3PlbE7E5BipQ3UjSebkgNboLHH/lNZA5qvEtvbfvQ==', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAut9evKRuHJ/2QNfDlLwvN/S8l9hRAgPbb0u61bm4AtzaTGsLeMtScetxTWJnVvAVpMS9luhEJjt+Sbk5TNLArsgzzwARgaTKOLMT1TvWAK5EbHyI+eSrc3s7Awe1VYGwcubRFWDm16eQLv0k7iqiw+4mweHSz/wWyvBJVgwLoQ02btVtAQErCfSJCOmt0Q/oJQjj08YNRV4EKzB19+f5A+HQVAKy72dSybTzAK+3FPtTtNen/+b5wGeat7c32dhYHnGorPkPeXLtsqqUTp1su5fMfd4lElNdZaoCI7osZxWWUo17vBCZnyeXc9fk0qwD9mK6yRAxNbrY72Xx5VqIqwIDAQAB', 'http://api.auauz.net/api/aliPay/return', 'RSA2', '2088102176044281');
COMMIT;

-- ----------------------------
-- Table structure for tool_email_config
-- ----------------------------
DROP TABLE IF EXISTS `tool_email_config`;
CREATE TABLE `tool_email_config` (
  `config_id` bigint NOT NULL COMMENT 'ID',
  `from_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收件人',
  `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮件服务器SMTP地址',
  `pass` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `port` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '端口',
  `user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发件者用户名',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='邮箱配置';

-- ----------------------------
-- Records of tool_email_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tool_local_storage
-- ----------------------------
DROP TABLE IF EXISTS `tool_local_storage`;
CREATE TABLE `tool_local_storage` (
  `storage_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件真实的名称',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名',
  `suffix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '后缀',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路径',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型',
  `size` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '大小',
  `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`storage_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='本地存储';

-- ----------------------------
-- Records of tool_local_storage
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tool_qiniu_config
-- ----------------------------
DROP TABLE IF EXISTS `tool_qiniu_config`;
CREATE TABLE `tool_qiniu_config` (
  `config_id` bigint NOT NULL COMMENT 'ID',
  `access_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'accessKey',
  `bucket` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Bucket 识别符',
  `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外链域名',
  `secret_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'secretKey',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '空间类型',
  `zone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '机房',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='七牛云配置';

-- ----------------------------
-- Records of tool_qiniu_config
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for tool_qiniu_content
-- ----------------------------
DROP TABLE IF EXISTS `tool_qiniu_content`;
CREATE TABLE `tool_qiniu_content` (
  `content_id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bucket` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Bucket 识别符',
  `name` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名称',
  `size` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件大小',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件类型：私有或公开',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件url',
  `suffix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件后缀',
  `update_time` datetime DEFAULT NULL COMMENT '上传或同步的时间',
  PRIMARY KEY (`content_id`) USING BTREE,
  UNIQUE KEY `uniq_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=COMPACT COMMENT='七牛云文件存储';

-- ----------------------------
-- Records of tool_qiniu_content
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;



SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for notice
-- ----------------------------
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice` (
  `id` varchar(255) NOT NULL COMMENT '消息id',
  `title` varchar(2000) DEFAULT NULL COMMENT '标题',
  `sub_title` varchar(2000) DEFAULT NULL COMMENT '副标题',
  `content` varchar(5000) DEFAULT NULL COMMENT '通知内容',
  `link` varchar(3000) DEFAULT NULL COMMENT '跳转链接',
  `status` varchar(100) DEFAULT NULL COMMENT '状态',
  `send_to` int DEFAULT NULL COMMENT '发送目标 1 所有用户 2 内测用户',
  `notify_time` bigint DEFAULT NULL COMMENT '通知时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

SET FOREIGN_KEY_CHECKS = 1;